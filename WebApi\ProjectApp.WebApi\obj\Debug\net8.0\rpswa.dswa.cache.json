{"GlobalPropertiesHash": "KKNDzqwzp5Emj+uYcQerZDsCnh63exyXZAbG/GYBiQ0=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["bjEE3mNQ1bvtjLtOU/AcRhF7QEn9T5tH48SIkl4+J78=", "P3w+4METLXSnjyJOjaIgeC+V0eQeB7MNfRWCFDD7DHQ=", "7MO+/Pb2WUiOKsYmKueoG/BQOmSQFs5LFY6F2GfZ0v4=", "brArzoscTxHWUKgBpCsa/nuyKBIrqKa9kKrZslFEys4=", "YvZegKhe8Rj430B4P1agF2HSvGD/kvTUXjmicctEh4c=", "02tnxuwJbUVWOILokEONcerS3VzvOQ92TLaqwFX3Hs4=", "nU4YsIwT3ic4EeSQZsBhp6glDkqD/a708DWKZsozuuI=", "0N9jU2ehkm+dLkW2HVbSjgri/UWt9pOLypouUtBSyZU=", "mk6nU5V1Pj4JixIirHuLEPUUWZB4n9VyAjIp59ywqjE=", "a0gCZ5WH63wIcL0mCyXYwgvnZFT+xzLhG03QljE+3DQ=", "OqW1OCHBCTd/Rr6rARTZhfWBwbYLKqjENBfWBbQcL5Y=", "C/7QMdGLdHrmVjGgYReigYzizCPDLsRNa4QTBqihecY=", "d2OvynQF3zJP9XiVpaiVbIz+qfkAyUfIAj/dAZWtZg8=", "hnRkpQTmO3VT1iacSZ7nMwAi1P7FSkErjS21RHcV3Hs=", "2TFb7E5qPHwWqKFlgPBCIYIrombkYuOgdbTW31T588s=", "vwhh6OrhoHxdEwfj/HBJjoSALfvmbXfJOQSoq5S56tM=", "HGpLRyYh0TrV0hsVqafGxBCINGgAI73ZMyj7aCP6AG4=", "9t1p45Ueh1ksmVk8gZcBYTvMBKkok71Bo0uuu6eIWSY=", "ge7WTPqvtMDXrvZRkPTubtJhSFw7dAysOI329iNur5U=", "K8c1epVme3QC4lCZcnCTaNKkeYoYaSBEdMg3kgeBi4Q=", "4qM8J+prQjuB9/+GNNyKOX2YHVaENBVozNg2G7ZsDRs=", "UKYZYZx6ByVkAE8qBDrCZ4WfkHWHMOfTRdbLsvazzoA=", "rXBa3Zj5vIScsa9M3d3dFg7jQZZ2HP1aQBenfiLTujw=", "aoI77NP8HqjTIP+u+bI2OJCJM7SVt5JN0HUJNy85t64=", "tB4Sr/rRHSum6lcqxtjOIiuwzIo/c8r8ysTGJ+RC3F8=", "bNnLyXp2WyMv0lXv0DHgcw0Bn8pR0SM1K2rZEMBCuLg=", "hmqVXsMoP/gkf+P+saDBOPDPlBgXe9vJm2pOChLBFBw=", "s1yWxujkS8Bywg1Uq9wXXshuX94AF8MZ66Zoj4KZ708=", "a9o5xK1BWMLGOmq5zJEd3AlqXdXJ/+GPdiKItMwCdnY=", "t8rm6siqi0IKEmh7Vyj3IjfVdwpxxQEdCUmSSvoNX5I=", "gv/WQCT6B9aklKLoib0dJ66lwy30U7bayu7RieYOu8o=", "O3uwVfZX0Ij1mnPpqSqC1ryCfpKENdDlAiQFEGxXl14=", "VcbYLnfKKrIGFw4RLzAdqjasF+H3+gWtvZn6ndnKXm8=", "8bzfx8ScZvMKamxvTyIA06/s3dGgCCnDm+EVOATTVm0=", "20HWeC+aiJIjvr/yrKnx8cAbTA+hxQdf5tu/fRbzrps=", "RoNWFlS3osMIrhNI0z80Zx4sMiqlBKjpfvdf+Urz+pU=", "ZfSgv9m+DjWZNp+HkmhCYHfcqOnmph8opyDSWqy7vpU=", "oLCtgHXa1C8fZs8htz7z6+/ueax0uU2cMMzVArraMSg=", "fVnDPBIQ+zO0Qa3/uMCBXmxGC9A030XwiPrfAJfP3ck=", "oW592osNIRu5p/9h3IrcQF+mKrlxJyOAPTxk7/BiuS0=", "mnApDrqZwEs9F4hOWoQsEjRcnNRJ21I7QtTvMa1Hu7o=", "h4jgLJpPmawDLSjoYWFn7mZpU0sUVYX0Ih3lZur86pM=", "fXMMUS8ZaYfdITm+ETuCHahN6Jy/6+CXhwmVOWh3cts=", "CwbF/8xr3lQiTP6S5dZwc8HK7n6xQCVvmvFCY5ehpG4=", "Q6p/Nj96e8QCmN3RmWkcstdpvNAoQu8nTAGcRh0EBCI=", "37k2wPG55t116qBEMsVy0F73pxwwcqW/vmwTnFYt5Bc=", "VB82IHkZjFq+iK0+gmtXQc2ZCfni4bpaXU8wE8x7xMY=", "LAFC0aAmDpcWirS7KP+IIMf6JTx50vEELCLRoFhTm3g=", "ABGxbfaAmsJn1ANlFO9UpvZXWoEXnHSMZGFzqJ8b0sQ=", "dkVL9Cuwv3Rme6iZTnjT3X3XVIL3EFMto6km2dN2H7o=", "tHch8t5jHNE/5yxPH8QKIfOU/tM8+POdJHwQqAf8xgc=", "ZdDHxOwZ4IyyYCyquAzFkTVm2ame3lmEA+UfY4SOvmg=", "VvXNgAfhhYPMazeG8uOk/IeYxXMKR3+aKJlVxK9Qy34=", "Bs3nxn0WDEYe+4V2OliQMoreJAVs5LJf+lOfoKO1fVA=", "qnUCH0MSgck81GZU2eNmqiPPo6/8vI7MgxW4tc+oCnQ=", "HkFUhFs0Zrc20EMlmHytWqKvnvsS/76qDbotInADHmI=", "PDJpeCeTu0SaafTaF74gVv3Znw7dh4f4jWIaNmtzbTM=", "K3nT0q+/S4ODzVSsq9U+qgrqxld3Oeuu/cfDUTk0LeI="], "CachedAssets": {"bjEE3mNQ1bvtjLtOU/AcRhF7QEn9T5tH48SIkl4+J78=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\.gitkeep", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "#[.{fingerprint}]?.gitkeep", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\.gitkeep", "FileLength": 0, "LastWriteTime": "2025-05-25T10:59:48.5239458+00:00"}, "P3w+4METLXSnjyJOjaIgeC+V0eQeB7MNfRWCFDD7DHQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\agent-chat-test.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "agent-chat-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2m651078tc", "Integrity": "usFFnQU4D9cPkeG1dil1t3lWgs6t+Ap1Wi9c+JkahO4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\agent-chat-test.html", "FileLength": 10511, "LastWriteTime": "2025-05-28T06:39:11.1690686+00:00"}, "7MO+/Pb2WUiOKsYmKueoG/BQOmSQFs5LFY6F2GfZ0v4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\index.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61b8v8xsty", "Integrity": "JmclD8wVnxXPeDAYYlY6ZkoWbccn5/qi8hPD//2KSC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 10912, "LastWriteTime": "2025-06-20T09:47:54.6159548+00:00"}, "brArzoscTxHWUKgBpCsa/nuyKBIrqKa9kKrZslFEys4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugin-details.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "plugin-details#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3az2yg0702", "Integrity": "G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\plugin-details.html", "FileLength": 9832, "LastWriteTime": "2025-04-15T03:14:27.565045+00:00"}, "YvZegKhe8Rj430B4P1agF2HSvGD/kvTUXjmicctEh4c=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugins.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "plugins#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "367d1cpt86", "Integrity": "dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\plugins.html", "FileLength": 23350, "LastWriteTime": "2025-04-15T03:14:23.8084216+00:00"}, "02tnxuwJbUVWOILokEONcerS3VzvOQ92TLaqwFX3Hs4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo-enhanced.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "process-framework-demo-enhanced#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "17i7mq3p3o", "Integrity": "3UXSomXtIDgOg5LUfrjLpnuXO1mXbH2AFN/xLtR+V+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\process-framework-demo-enhanced.html", "FileLength": 28458, "LastWriteTime": "2025-06-20T09:47:10.9187734+00:00"}, "nU4YsIwT3ic4EeSQZsBhp6glDkqD/a708DWKZsozuuI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "process-framework-demo#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lys8hslai6", "Integrity": "nbXoqtXOyIhly1wZB3ZJj9qs3SDlD9HfqQnr+wJYvKA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\process-framework-demo.html", "FileLength": 11201, "LastWriteTime": "2025-06-20T09:45:30.4629613+00:00"}, "0N9jU2ehkm+dLkW2HVbSjgri/UWt9pOLypouUtBSyZU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\apexmakerclub5.png", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/apexmakerclub5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d5944pppt6", "Integrity": "r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\apexmakerclub5.png", "FileLength": 57189, "LastWriteTime": "2025-06-04T13:20:34.3178527+00:00"}, "mk6nU5V1Pj4JixIirHuLEPUUWZB4n9VyAjIp59ywqjE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image.jpg", "FileLength": 56443, "LastWriteTime": "2025-03-31T11:23:23.6464851+00:00"}, "a0gCZ5WH63wIcL0mCyXYwgvnZFT+xzLhG03QljE+3DQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpeg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_1#[.{fingerprint}]?.jpeg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_1.jpeg", "FileLength": 56443, "LastWriteTime": "2025-05-19T09:48:39.8868428+00:00"}, "OqW1OCHBCTd/Rr6rARTZhfWBwbYLKqjENBfWBbQcL5Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_1.jpg", "FileLength": 56443, "LastWriteTime": "2025-03-31T11:23:58.9084681+00:00"}, "C/7QMdGLdHrmVjGgYReigYzizCPDLsRNa4QTBqihecY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_2.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_2.jpg", "FileLength": 56443, "LastWriteTime": "2025-04-01T03:30:54.5941247+00:00"}, "d2OvynQF3zJP9XiVpaiVbIz+qfkAyUfIAj/dAZWtZg8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_3.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_3.jpg", "FileLength": 56443, "LastWriteTime": "2025-04-01T03:38:04.0342121+00:00"}, "hnRkpQTmO3VT1iacSZ7nMwAi1P7FSkErjS21RHcV3Hs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_4.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_4#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_4.jpg", "FileLength": 56443, "LastWriteTime": "2025-05-19T09:43:16.412024+00:00"}, "2TFb7E5qPHwWqKFlgPBCIYIrombkYuOgdbTW31T588s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-16T04:28:27.4189438+00:00"}, "vwhh6OrhoHxdEwfj/HBJjoSALfvmbXfJOQSoq5S56tM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-16T09:34:54.5061706+00:00"}, "HGpLRyYh0TrV0hsVqafGxBCINGgAI73ZMyj7aCP6AG4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T01:54:28.74283+00:00"}, "9t1p45Ueh1ksmVk8gZcBYTvMBKkok71Bo0uuu6eIWSY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T03:02:09.3549404+00:00"}, "ge7WTPqvtMDXrvZRkPTubtJhSFw7dAysOI329iNur5U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T03:49:10.5045807+00:00"}, "K8c1epVme3QC4lCZcnCTaNKkeYoYaSBEdMg3kgeBi4Q=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T08:51:27.4713073+00:00"}, "4qM8J+prQjuB9/+GNNyKOX2YHVaENBVozNg2G7ZsDRs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\landingPage.png", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/landingPage#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "929y9semqo", "Integrity": "YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\landingPage.png", "FileLength": 292001, "LastWriteTime": "2025-03-24T19:56:44.7971483+00:00"}, "UKYZYZx6ByVkAE8qBDrCZ4WfkHWHMOfTRdbLsvazzoA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/maxresdefault#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x27hb5tlzi", "Integrity": "GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\maxresdefault.jpg", "FileLength": 99558, "LastWriteTime": "2025-05-19T09:53:51.9899418+00:00"}, "rXBa3Zj5vIScsa9M3d3dFg7jQZZ2HP1aQBenfiLTujw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/maxresdefault_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x27hb5tlzi", "Integrity": "GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\maxresdefault_1.jpg", "FileLength": 99558, "LastWriteTime": "2025-05-19T09:54:04.4085704+00:00"}, "aoI77NP8HqjTIP+u+bI2OJCJM7SVt5JN0HUJNy85t64=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.csv", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/name#[.{fingerprint}]?.csv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gze1rkoz4g", "Integrity": "5PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\name.csv", "FileLength": 32, "LastWriteTime": "2025-06-04T12:59:12.011141+00:00"}, "tB4Sr/rRHSum6lcqxtjOIiuwzIo/c8r8ysTGJ+RC3F8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.xlsx", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/name#[.{fingerprint}]?.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vskwaa03f5", "Integrity": "T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\name.xlsx", "FileLength": 4748, "LastWriteTime": "2025-06-04T12:57:22.8289164+00:00"}, "bNnLyXp2WyMv0lXv0DHgcw0Bn8pR0SM1K2rZEMBCuLg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-25T11:12:29.1843579+00:00"}, "hmqVXsMoP/gkf+P+saDBOPDPlBgXe9vJm2pOChLBFBw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T04:48:17.7405514+00:00"}, "s1yWxujkS8Bywg1Uq9wXXshuX94AF8MZ66Zoj4KZ708=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T04:23:29.0587059+00:00"}, "a9o5xK1BWMLGOmq5zJEd3AlqXdXJ/+GPdiKItMwCdnY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_3#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T04:26:51.2132174+00:00"}, "t8rm6siqi0IKEmh7Vyj3IjfVdwpxxQEdCUmSSvoNX5I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T06:57:45.3863607+00:00"}, "gv/WQCT6B9aklKLoib0dJ66lwy30U7bayu7RieYOu8o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-19T02:39:32.2025149+00:00"}, "O3uwVfZX0Ij1mnPpqSqC1ryCfpKENdDlAiQFEGxXl14=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-19T03:35:23.7296531+00:00"}, "VcbYLnfKKrIGFw4RLzAdqjasF+H3+gWtvZn6ndnKXm8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_7#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-19T05:09:55.8904163+00:00"}, "8bzfx8ScZvMKamxvTyIA06/s3dGgCCnDm+EVOATTVm0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T04:15:39.1172206+00:00"}, "20HWeC+aiJIjvr/yrKnx8cAbTA+hxQdf5tu/fRbzrps=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:11:15.666235+00:00"}, "RoNWFlS3osMIrhNI0z80Zx4sMiqlBKjpfvdf+Urz+pU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:12:08.9031062+00:00"}, "ZfSgv9m+DjWZNp+HkmhCYHfcqOnmph8opyDSWqy7vpU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_2.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:13:50.2207399+00:00"}, "oLCtgHXa1C8fZs8htz7z6+/ueax0uU2cMMzVArraMSg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_3.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_3#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_3.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:26:31.4862516+00:00"}, "fVnDPBIQ+zO0Qa3/uMCBXmxGC9A030XwiPrfAJfP3ck=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_4.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:29:40.9983876+00:00"}, "oW592osNIRu5p/9h3IrcQF+mKrlxJyOAPTxk7/BiuS0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_amardeep.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt_amardeep#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt_amardeep.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T05:33:15.6191948+00:00"}, "mnApDrqZwEs9F4hOWoQsEjRcnNRJ21I7QtTvMa1Hu7o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt_shyam#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt_shyam.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T10:39:47.7426485+00:00"}, "h4jgLJpPmawDLSjoYWFn7mZpU0sUVYX0Ih3lZur86pM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt_shyam_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt_shyam_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T10:55:37.3185063+00:00"}, "fXMMUS8ZaYfdITm+ETuCHahN6Jy/6+CXhwmVOWh3cts=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/robot leg#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hk1hg0l2za", "Integrity": "MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\robot leg.jpg", "FileLength": 47187, "LastWriteTime": "2025-04-03T01:14:49.4984155+00:00"}, "CwbF/8xr3lQiTP6S5dZwc8HK7n6xQCVvmvFCY5ehpG4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/robot leg_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hk1hg0l2za", "Integrity": "MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\robot leg_1.jpg", "FileLength": 47187, "LastWriteTime": "2025-04-03T01:18:44.0080696+00:00"}, "LAFC0aAmDpcWirS7KP+IIMf6JTx50vEELCLRoFhTm3g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.docx", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/test#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lacrko49p0", "Integrity": "/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\test.docx", "FileLength": 6507, "LastWriteTime": "2025-06-04T13:01:56.5661439+00:00"}, "ABGxbfaAmsJn1ANlFO9UpvZXWoEXnHSMZGFzqJ8b0sQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/test#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "54emubzsk1", "Integrity": "C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB+8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\test.txt", "FileLength": 38, "LastWriteTime": "2025-06-04T13:03:28.1759025+00:00"}, "dkVL9Cuwv3Rme6iZTnjT3X3XVIL3EFMto6km2dN2H7o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\text.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/text#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4rhg4rlloj", "Integrity": "IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\text.jpg", "FileLength": 109894, "LastWriteTime": "2025-06-04T13:28:21.2523699+00:00"}, "tHch8t5jHNE/5yxPH8QKIfOU/tM8+POdJHwQqAf8xgc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe.jpg", "FileLength": 25704, "LastWriteTime": "2025-05-01T12:30:29.7937577+00:00"}, "ZdDHxOwZ4IyyYCyquAzFkTVm2ame3lmEA+UfY4SOvmg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe_1.jpg", "FileLength": 25704, "LastWriteTime": "2025-05-01T12:44:25.586309+00:00"}, "VvXNgAfhhYPMazeG8uOk/IeYxXMKR3+aKJlVxK9Qy34=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_2.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe_2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe_2.jpg", "FileLength": 25704, "LastWriteTime": "2025-05-01T12:46:50.0984982+00:00"}, "Bs3nxn0WDEYe+4V2OliQMoreJAVs5LJf+lOfoKO1fVA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow-demo-enhanced.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "workflow-demo-enhanced#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "7ARxMipZYjf/8x4gRKc+2lGjbPHka8mSXbQF9+li3pE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\workflow-demo-enhanced.html", "FileLength": 27351, "LastWriteTime": "2025-06-23T08:23:00.0727678+00:00"}, "qnUCH0MSgck81GZU2eNmqiPPo6/8vI7MgxW4tc+oCnQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "workflow#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\workflow.html", "FileLength": 0, "LastWriteTime": "2025-06-19T13:30:34.8939275+00:00"}, "Q6p/Nj96e8QCmN3RmWkcstdpvNAoQu8nTAGcRh0EBCI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/sql#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o2p4fx5oup", "Integrity": "8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\sql.txt", "FileLength": 1657, "LastWriteTime": "2025-06-23T20:45:47.2703395+00:00"}, "37k2wPG55t116qBEMsVy0F73pxwwcqW/vmwTnFYt5Bc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_1.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/sql_1#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o2p4fx5oup", "Integrity": "8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\sql_1.txt", "FileLength": 1657, "LastWriteTime": "2025-06-23T20:49:15.0937786+00:00"}, "VB82IHkZjFq+iK0+gmtXQc2ZCfni4bpaXU8wE8x7xMY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_2.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/sql_2#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o2p4fx5oup", "Integrity": "8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\sql_2.txt", "FileLength": 1657, "LastWriteTime": "2025-06-23T20:51:58.9658991+00:00"}}, "CachedCopyCandidates": {}}