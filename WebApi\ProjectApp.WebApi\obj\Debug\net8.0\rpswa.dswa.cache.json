{"GlobalPropertiesHash": "KKNDzqwzp5Emj+uYcQerZDsCnh63exyXZAbG/GYBiQ0=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["/DAsSa4eSY8iSkehOTdWoIhKd0DDmWUf8ImxvhzLxmQ=", "f704nNhzbSi2IKLcZRs9c+Ed+sLoJbvVZ2rHf2MZ21A=", "fopb2fcaGyyU8RKkJJw2Saq4FpwgQj4CLIisjWeYe1c=", "jZgmW0m4BuXuQEqE889Bcvo61FMVP+5Q1ZRxBdfti50=", "7vqsfuoDmhShc0QOTV9pxnzIlCu4nNOFUqjplPGARJU=", "vF52+PFBjTG3NLA1VX01lorw/F0sxfGm6+jiVePDeiw=", "HNtgysn6503gC2NHmGs29njIOOLLd28driRtqBnja8g=", "YiHUn3xFPKj7JxFMsg4mV9km4ti3s6oBP6uw45XQ670=", "/2X6NIvsDxr0kSVf/Gc3C2iXUe7w2qjNLfm2qw+D6yA=", "S1ks5cleNGde+boLANRJKUmcZYsqTaw1JWD6ImWGStg=", "1V6fFknaNoMXA2Wn6Ro97Hc6G29gi7Ca/SynD4e9esI=", "gAceaP5ir2D1aq033uhMF8qyiCUqiKko/FQXHpcgloo=", "H2jOY1A8c6crWBZLIZ6Ny0rD09Zntfee+eBewTI8h9s=", "DjqJqSxGUPlPP5EVcmiBVkvrl54+9rqxJqjVRQnmUdI=", "DCO84TRdnK6uH13C1zXBXCqlXQbFNYEhyTXvQPpTPU0=", "p8K90HvKgXoupjjQAhHD4iAPn/trKOImlePILXWSe8k=", "WCvU2WUstNzC/Qp6Z0+OGl06LoFX9xwyE+qgZpWmKUw=", "TmMbsfnNQaNsZETiAYxoi931YXTOCloZXr3vEfGW6x8=", "qZsEokbuWJCGV9lRxJkHRDPHV0PJbU0MeOsOcOYv5bs=", "Y9V71omsWy0QrJXDo2j8csaUhSxTNaRRzEewcDSbpoM=", "mL4uNO8P58lqoQ1AL0VOJgm70APFW/Kw51s867eE92s=", "HnVl3zYJq22h1u97m7K+Ll/XcJH7pO61Hw5/TwWsubA=", "+/2q0hoI137G7nVcZwGhzuETgqD1/SYHF5TfUMmDNpc=", "21R0mYtHojva/CWeqmBiWiSATTcjDuYFkem0+Dweae4=", "IFeqzYOKVjLmYV+I0FlLK+/POGl1cAprBQIXYy2dViE=", "WZ1AnjNhjldxF7IiUf+qw9ry8hBNFYCqMEHW4kd7VmE=", "JtXMQh0tM1AH/1eEH9ffsVzb8p4dhx3zaMLKY7awrsE=", "Ab9pw6k3UFHIO8gtQotvtxQ7UA29Sae7hRzcln7oG+o=", "S43Oo2+sC0Y+Y6mRfWOsKU1Krtkg16jsJosGeyCGXxI=", "Do3kTnUwPtaQZOZKB19/XB+To5CPheC0AP5xByY/CsY=", "umIC455JatvrMfwG/oyY/6TtrmG6H2MUixXW0ari62o=", "ALTQc5mBbzwZiQfVDa1+PcShwAfkWYi4J3Q+S3rakDA=", "H9RFZkOIjq9DyUx1HAqwrlo9IG3MYiSbRKCThJFIpjI=", "B3KBMaCyq2smP2M2OMSSBaIrLxYZ2QaGd0gH4ymFwn8=", "+ODIOn80YkOGw8Cxy7zoDMfP0cKiJLR47hf8HwBCmK4=", "neuIl5XUhc4+sokLK5WDGxrXvkKWO7crBvNWUwp1y/Y=", "1HG4QfY3UleLSywGc348xTB7BFYhS0JC71uvwiQ7e+k=", "vWVzXbQfsz8/uLbu+pWw5hQWhVBbT8VPL8MgxWMrKlo=", "tSKTlfYcr47oqlsmv8tVVGzFYdoH5eMMqUJAicodii0=", "2wc/QKKHTcdRW7WiId68XyQI9Hwd7WmOF7nQ4iI/lJc=", "q3cV4MAeYLg0vx7tg+i9qg34egleBsdMzZV+3x4UJqE=", "WADwJaFKTlDFkvI+DG4Rw5TaC308SLG+y7nHhMNGSrA=", "TSs/KS3dxnPHXeSvLI1C84ZjZ/DtDDbsWkWdUDnQC3U=", "GSYDHw0f9Y0dsPunEFmpqBCWef6v6KDFf8YxzEpYrfA=", "1hyCoJQ8+qlf6riN0mUHoH6+7fMeYJZx8qjm5qHoqxY=", "Dgk3TwXSUrKpbhUIOHz57GXzDXzdf3NUEPhG0Y/GuMc=", "Q5w/lM0PgY76eSI7becfXvQl/vyJistXN8b5k1DQiaM=", "JfKP3pFI3vGwDGEWtozSwXHPuovmpQku4e3uFr9DRBY=", "Hc+m/G2DvYMmKKHn8q7hkB8IkFVdbX6AY8DLgg3Zt/k=", "r4/YrJGBH1BW9R1O9ZDwBnNLnS41gqbPB0yvEhn+0XI=", "ILBrNJ1bjXalSp56TdxGFrbX9jeMc5bqwNApWnnKRKM=", "Ojo1h3Vk8xhJtes8fg2Z5Ye4DKa7RDs+YS/Ga8clVfY=", "3Va2Y4eLU5Vn88kKAdBuzOo+yEIENT9tU2k3UfJ8YEQ=", "f1ftNqqhqxhCFvHd0Up6U3llRMfbSdTYADtF4SpefBY=", "9Ajwh4lgOJfbnAuw2sBgObcXn7pjltlcPSHwie4fZq0=", "HkFUhFs0Zrc20EMlmHytWqKvnvsS/76qDbotInADHmI=", "PDJpeCeTu0SaafTaF74gVv3Znw7dh4f4jWIaNmtzbTM=", "K3nT0q+/S4ODzVSsq9U+qgrqxld3Oeuu/cfDUTk0LeI="], "CachedAssets": {"9Ajwh4lgOJfbnAuw2sBgObcXn7pjltlcPSHwie4fZq0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "workflow#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\workflow.html", "FileLength": 0, "LastWriteTime": "2025-06-19T13:30:34.8939275+00:00"}, "f1ftNqqhqxhCFvHd0Up6U3llRMfbSdTYADtF4SpefBY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow-demo-enhanced.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "workflow-demo-enhanced#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "7ARxMipZYjf/8x4gRKc+2lGjbPHka8mSXbQF9+li3pE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\workflow-demo-enhanced.html", "FileLength": 27351, "LastWriteTime": "2025-06-23T08:23:00.0727678+00:00"}, "3Va2Y4eLU5Vn88kKAdBuzOo+yEIENT9tU2k3UfJ8YEQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_2.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe_2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe_2.jpg", "FileLength": 25704, "LastWriteTime": "2025-05-01T12:46:50.0984982+00:00"}, "Ojo1h3Vk8xhJtes8fg2Z5Ye4DKa7RDs+YS/Ga8clVfY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe_1.jpg", "FileLength": 25704, "LastWriteTime": "2025-05-01T12:44:25.586309+00:00"}, "ILBrNJ1bjXalSp56TdxGFrbX9jeMc5bqwNApWnnKRKM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe.jpg", "FileLength": 25704, "LastWriteTime": "2025-05-01T12:30:29.7937577+00:00"}, "r4/YrJGBH1BW9R1O9ZDwBnNLnS41gqbPB0yvEhn+0XI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\text.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/text#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4rhg4rlloj", "Integrity": "IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\text.jpg", "FileLength": 109894, "LastWriteTime": "2025-06-04T13:28:21.2523699+00:00"}, "Hc+m/G2DvYMmKKHn8q7hkB8IkFVdbX6AY8DLgg3Zt/k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/test#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "54emubzsk1", "Integrity": "C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB+8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\test.txt", "FileLength": 38, "LastWriteTime": "2025-06-04T13:03:28.1759025+00:00"}, "JfKP3pFI3vGwDGEWtozSwXHPuovmpQku4e3uFr9DRBY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.docx", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/test#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lacrko49p0", "Integrity": "/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\test.docx", "FileLength": 6507, "LastWriteTime": "2025-06-04T13:01:56.5661439+00:00"}, "Q5w/lM0PgY76eSI7becfXvQl/vyJistXN8b5k1DQiaM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_2.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/sql_2#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o2p4fx5oup", "Integrity": "8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\sql_2.txt", "FileLength": 1657, "LastWriteTime": "2025-06-23T20:51:58.9658991+00:00"}, "Dgk3TwXSUrKpbhUIOHz57GXzDXzdf3NUEPhG0Y/GuMc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_1.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/sql_1#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o2p4fx5oup", "Integrity": "8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\sql_1.txt", "FileLength": 1657, "LastWriteTime": "2025-06-23T20:49:15.0937786+00:00"}, "1hyCoJQ8+qlf6riN0mUHoH6+7fMeYJZx8qjm5qHoqxY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/sql#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o2p4fx5oup", "Integrity": "8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\sql.txt", "FileLength": 1657, "LastWriteTime": "2025-06-23T20:45:47.2703395+00:00"}, "GSYDHw0f9Y0dsPunEFmpqBCWef6v6KDFf8YxzEpYrfA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/robot leg_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hk1hg0l2za", "Integrity": "MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\robot leg_1.jpg", "FileLength": 47187, "LastWriteTime": "2025-04-03T01:18:44.0080696+00:00"}, "TSs/KS3dxnPHXeSvLI1C84ZjZ/DtDDbsWkWdUDnQC3U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/robot leg#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hk1hg0l2za", "Integrity": "MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\robot leg.jpg", "FileLength": 47187, "LastWriteTime": "2025-04-03T01:14:49.4984155+00:00"}, "WADwJaFKTlDFkvI+DG4Rw5TaC308SLG+y7nHhMNGSrA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt_shyam_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt_shyam_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T10:55:37.3185063+00:00"}, "q3cV4MAeYLg0vx7tg+i9qg34egleBsdMzZV+3x4UJqE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt_shyam#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt_shyam.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T10:39:47.7426485+00:00"}, "2wc/QKKHTcdRW7WiId68XyQI9Hwd7WmOF7nQ4iI/lJc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_amardeep.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt_amardeep#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt_amardeep.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T05:33:15.6191948+00:00"}, "tSKTlfYcr47oqlsmv8tVVGzFYdoH5eMMqUJAicodii0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_4.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:29:40.9983876+00:00"}, "vWVzXbQfsz8/uLbu+pWw5hQWhVBbT8VPL8MgxWMrKlo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_3.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_3#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_3.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:26:31.4862516+00:00"}, "1HG4QfY3UleLSywGc348xTB7BFYhS0JC71uvwiQ7e+k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_2.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:13:50.2207399+00:00"}, "neuIl5XUhc4+sokLK5WDGxrXvkKWO7crBvNWUwp1y/Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:12:08.9031062+00:00"}, "+ODIOn80YkOGw8Cxy7zoDMfP0cKiJLR47hf8HwBCmK4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:11:15.666235+00:00"}, "B3KBMaCyq2smP2M2OMSSBaIrLxYZ2QaGd0gH4ymFwn8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T04:15:39.1172206+00:00"}, "H9RFZkOIjq9DyUx1HAqwrlo9IG3MYiSbRKCThJFIpjI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_7#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-19T05:09:55.8904163+00:00"}, "ALTQc5mBbzwZiQfVDa1+PcShwAfkWYi4J3Q+S3rakDA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-19T03:35:23.7296531+00:00"}, "umIC455JatvrMfwG/oyY/6TtrmG6H2MUixXW0ari62o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-19T02:39:32.2025149+00:00"}, "Do3kTnUwPtaQZOZKB19/XB+To5CPheC0AP5xByY/CsY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T06:57:45.3863607+00:00"}, "S43Oo2+sC0Y+Y6mRfWOsKU1Krtkg16jsJosGeyCGXxI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_3#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T04:26:51.2132174+00:00"}, "Ab9pw6k3UFHIO8gtQotvtxQ7UA29Sae7hRzcln7oG+o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T04:23:29.0587059+00:00"}, "JtXMQh0tM1AH/1eEH9ffsVzb8p4dhx3zaMLKY7awrsE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T04:48:17.7405514+00:00"}, "WZ1AnjNhjldxF7IiUf+qw9ry8hBNFYCqMEHW4kd7VmE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-25T11:12:29.1843579+00:00"}, "IFeqzYOKVjLmYV+I0FlLK+/POGl1cAprBQIXYy2dViE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.xlsx", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/name#[.{fingerprint}]?.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vskwaa03f5", "Integrity": "T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\name.xlsx", "FileLength": 4748, "LastWriteTime": "2025-06-04T12:57:22.8289164+00:00"}, "21R0mYtHojva/CWeqmBiWiSATTcjDuYFkem0+Dweae4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.csv", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/name#[.{fingerprint}]?.csv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gze1rkoz4g", "Integrity": "5PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\name.csv", "FileLength": 32, "LastWriteTime": "2025-06-04T12:59:12.011141+00:00"}, "+/2q0hoI137G7nVcZwGhzuETgqD1/SYHF5TfUMmDNpc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/maxresdefault_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x27hb5tlzi", "Integrity": "GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\maxresdefault_1.jpg", "FileLength": 99558, "LastWriteTime": "2025-05-19T09:54:04.4085704+00:00"}, "HnVl3zYJq22h1u97m7K+Ll/XcJH7pO61Hw5/TwWsubA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/maxresdefault#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x27hb5tlzi", "Integrity": "GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\maxresdefault.jpg", "FileLength": 99558, "LastWriteTime": "2025-05-19T09:53:51.9899418+00:00"}, "mL4uNO8P58lqoQ1AL0VOJgm70APFW/Kw51s867eE92s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\landingPage.png", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/landingPage#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "929y9semqo", "Integrity": "YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\landingPage.png", "FileLength": 292001, "LastWriteTime": "2025-03-24T19:56:44.7971483+00:00"}, "Y9V71omsWy0QrJXDo2j8csaUhSxTNaRRzEewcDSbpoM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T08:51:27.4713073+00:00"}, "qZsEokbuWJCGV9lRxJkHRDPHV0PJbU0MeOsOcOYv5bs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T03:49:10.5045807+00:00"}, "TmMbsfnNQaNsZETiAYxoi931YXTOCloZXr3vEfGW6x8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T03:02:09.3549404+00:00"}, "WCvU2WUstNzC/Qp6Z0+OGl06LoFX9xwyE+qgZpWmKUw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T01:54:28.74283+00:00"}, "p8K90HvKgXoupjjQAhHD4iAPn/trKOImlePILXWSe8k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-16T09:34:54.5061706+00:00"}, "DCO84TRdnK6uH13C1zXBXCqlXQbFNYEhyTXvQPpTPU0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-16T04:28:27.4189438+00:00"}, "DjqJqSxGUPlPP5EVcmiBVkvrl54+9rqxJqjVRQnmUdI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_4.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_4#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_4.jpg", "FileLength": 56443, "LastWriteTime": "2025-05-19T09:43:16.412024+00:00"}, "H2jOY1A8c6crWBZLIZ6Ny0rD09Zntfee+eBewTI8h9s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_3.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_3.jpg", "FileLength": 56443, "LastWriteTime": "2025-04-01T03:38:04.0342121+00:00"}, "gAceaP5ir2D1aq033uhMF8qyiCUqiKko/FQXHpcgloo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_2.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_2.jpg", "FileLength": 56443, "LastWriteTime": "2025-04-01T03:30:54.5941247+00:00"}, "YiHUn3xFPKj7JxFMsg4mV9km4ti3s6oBP6uw45XQ670=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\apexmakerclub5.png", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/apexmakerclub5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d5944pppt6", "Integrity": "r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\apexmakerclub5.png", "FileLength": 57189, "LastWriteTime": "2025-06-04T13:20:34.3178527+00:00"}, "HNtgysn6503gC2NHmGs29njIOOLLd28driRtqBnja8g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "process-framework-demo#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lys8hslai6", "Integrity": "nbXoqtXOyIhly1wZB3ZJj9qs3SDlD9HfqQnr+wJYvKA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\process-framework-demo.html", "FileLength": 11201, "LastWriteTime": "2025-06-20T09:45:30.4629613+00:00"}, "vF52+PFBjTG3NLA1VX01lorw/F0sxfGm6+jiVePDeiw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo-enhanced.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "process-framework-demo-enhanced#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "17i7mq3p3o", "Integrity": "3UXSomXtIDgOg5LUfrjLpnuXO1mXbH2AFN/xLtR+V+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\process-framework-demo-enhanced.html", "FileLength": 28458, "LastWriteTime": "2025-06-20T09:47:10.9187734+00:00"}, "7vqsfuoDmhShc0QOTV9pxnzIlCu4nNOFUqjplPGARJU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugins.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "plugins#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "367d1cpt86", "Integrity": "dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\plugins.html", "FileLength": 23350, "LastWriteTime": "2025-04-15T03:14:23.8084216+00:00"}, "jZgmW0m4BuXuQEqE889Bcvo61FMVP+5Q1ZRxBdfti50=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugin-details.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "plugin-details#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3az2yg0702", "Integrity": "G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\plugin-details.html", "FileLength": 9832, "LastWriteTime": "2025-04-15T03:14:27.565045+00:00"}, "fopb2fcaGyyU8RKkJJw2Saq4FpwgQj4CLIisjWeYe1c=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\index.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61b8v8xsty", "Integrity": "JmclD8wVnxXPeDAYYlY6ZkoWbccn5/qi8hPD//2KSC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 10912, "LastWriteTime": "2025-06-20T09:47:54.6159548+00:00"}, "f704nNhzbSi2IKLcZRs9c+Ed+sLoJbvVZ2rHf2MZ21A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\agent-chat-test.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "agent-chat-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2m651078tc", "Integrity": "usFFnQU4D9cPkeG1dil1t3lWgs6t+Ap1Wi9c+JkahO4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\agent-chat-test.html", "FileLength": 10511, "LastWriteTime": "2025-05-28T06:39:11.1690686+00:00"}, "/DAsSa4eSY8iSkehOTdWoIhKd0DDmWUf8ImxvhzLxmQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\.gitkeep", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "#[.{fingerprint}]?.gitkeep", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\.gitkeep", "FileLength": 0, "LastWriteTime": "2025-05-25T10:59:48.5239458+00:00"}, "1V6fFknaNoMXA2Wn6Ro97Hc6G29gi7Ca/SynD4e9esI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_1.jpg", "FileLength": 56443, "LastWriteTime": "2025-03-31T11:23:58.9084681+00:00"}, "S1ks5cleNGde+boLANRJKUmcZYsqTaw1JWD6ImWGStg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpeg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_1#[.{fingerprint}]?.jpeg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_1.jpeg", "FileLength": 56443, "LastWriteTime": "2025-05-19T09:48:39.8868428+00:00"}, "/2X6NIvsDxr0kSVf/Gc3C2iXUe7w2qjNLfm2qw+D6yA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image.jpg", "FileLength": 56443, "LastWriteTime": "2025-03-31T11:23:23.6464851+00:00"}}, "CachedCopyCandidates": {}}