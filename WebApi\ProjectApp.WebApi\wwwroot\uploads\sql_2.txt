[{"workspace": "Developer", "projectDescription": "I have the ability to generate SQL related stuff", "projectCategory": "DataBaseManager"}, {"workspace": "Ad Manager", "projectDescription": "Analyzes ad performance using complex SQL queries", "projectCategory": "AdAnalytics"}, {"workspace": "Developer", "projectDescription": "Develops backend systems using SQL for data storage", "projectCategory": "WebDevelopment"}, {"workspace": "Ad Manager", "projectDescription": "Uses SQL to generate reports for campaign optimization", "projectCategory": "MarketingReports"}, {"workspace": "Developer", "projectDescription": "Writes optimized SQL queries for high-performance apps", "projectCategory": "PerformanceTuning"}, {"workspace": "Ad Manager", "projectDescription": "Extracts audience insights with SQL-based data tools", "projectCategory": "AudienceSegmentation"}, {"workspace": "Developer", "projectDescription": "Implements SQL logic in data-driven applications", "projectCategory": "ApplicationDevelopment"}, {"workspace": "Ad Manager", "projectDescription": "Builds dashboards with SQL data sources for ad KPIs", "projectCategory": "Dashboarding"}, {"workspace": "Developer", "projectDescription": "Creates and maintains SQL-based APIs", "projectCategory": "DataServices"}, {"workspace": "Ad Manager", "projectDescription": "Manages ad inventory and revenue reports using SQL", "projectCategory": "RevenueTracking"}]