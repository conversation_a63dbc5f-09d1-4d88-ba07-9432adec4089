﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IWorkspaceRepository
    {
        Task<List<WorkspaceDto>> GetAll();
        Task<WorkspaceDto> GetById(int id);
        Task<WorkspaceDto> GetByTitle(string title);
        Task<Workspace> CreateOrUpdate(Workspace request);
        Task<Workspace> Delete(int id);
        Task<List<WorkspaceDto>> GetWorkspacesForUser();
        Task<Workspace> GetDefaultWorkspace();
        Task<DateTime> GetLastCompletionDate(string assignedEmail);
        Task<bool> ToggleFavorite(int workspaceId);
        Task<List<WorkspaceDto>> GetFavorites();
    }
}
