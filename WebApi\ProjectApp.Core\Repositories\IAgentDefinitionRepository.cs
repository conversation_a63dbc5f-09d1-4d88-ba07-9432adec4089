﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;

namespace ProjectApp.Core.Repositories
{
    public interface IAgentDefinitionRepository
    {
        Task<IEnumerable<AgentDefinition>> GetAllAsync();
        Task<IEnumerable<AgentDefinition>> GetAllByWorkspace(string worksapce);
        Task<IEnumerable<AgentNameDto>> GetAllAgentName();
        Task<int> TotalAgentCountInWorkspace(string workspace);
        Task<AgentDefinition> GetByAgentName(string agentName);
        Task<AgentDefinition> CreateOrUpdate(AgentDefinitionDto createAgentDefinitionDto);
        Task<bool> DeleteByAgentName(string agentName);
        Task SyncToMemory();
        Task<bool> ToggleFavorite(string agentName);
        Task<IEnumerable<AgentDefinition>> GetFavorites();
        //Task<ResponseMessage> CallAgent(string agentName, string question);
    }
}
