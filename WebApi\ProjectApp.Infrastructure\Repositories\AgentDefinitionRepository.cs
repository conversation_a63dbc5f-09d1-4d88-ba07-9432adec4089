﻿using AutoMapper;
using Dapper;
using Microsoft.KernelMemory;
using NetTopologySuite.Utilities;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class AgentDefinitionRepository(IDbConnection _dbConnection, IMapper _mapper, IKernelMemory _memory) : IAgentDefinitionRepository
    {

        public async Task<IEnumerable<AgentDefinition>> GetAllAsync()
        {
            var sql = "SELECT * FROM AgentDefinitions";
            return await _dbConnection.QueryAsync<AgentDefinition>(sql);
        }
        public async Task<IEnumerable<AgentNameDto>> GetAllAgentName()
        {
            var sql = "SELECT * FROM AgentDefinitions";
            return await _dbConnection.QueryAsync<AgentNameDto>(sql);
        }
        public async Task<IEnumerable<AgentDefinition>> GetAllByWorkspace(string workspace)
        {
            var sql = "SELECT * FROM AgentDefinitions where Workspace = @Workspace";
            return await _dbConnection.QueryAsync<AgentDefinition>(sql, new { Workspace = workspace });
        }

        public async Task<AgentDefinition> GetByAgentName(string agentName)
        {
            var sql = "SELECT * FROM AgentDefinitions WHERE AgentName = @AgentName";
            return await _dbConnection.QueryFirstOrDefaultAsync<AgentDefinition>(sql, new { AgentName = agentName });
        }

        public async Task<AgentDefinition> CreateOrUpdate(AgentDefinitionDto createAgentDefinitionDto)
        {
            var existingAgent = await GetByAgentName(createAgentDefinitionDto.AgentName);

            if (existingAgent == null)
            {
                var memoryTags = new List<MemoryTag>
                {
                    new MemoryTag { Name = "WorkspaceName", Value = createAgentDefinitionDto.Workspace }
                };


                var newAgentDefinition = await Create(createAgentDefinitionDto);
                var text = $"AgentName: {newAgentDefinition.AgentName} \nAgentDescription: {newAgentDefinition.UserInstructions}";
                await AddMemory(text, newAgentDefinition.Guid.ToString(), "Agent", memoryTags);

                return newAgentDefinition;
            }
            else
            {
                await DeleteSingleMemory(existingAgent.Guid.ToString(), "Agent");

                _mapper.Map(createAgentDefinitionDto, existingAgent); // Map the DTO to the existing entity  

                var memoryTags = new List<MemoryTag>
                {
                    new MemoryTag { Name = "WorkspaceName", Value = existingAgent.Workspace }
                };

                var text = $"AgentName: {existingAgent.AgentName} \nAgentDescription: {existingAgent.UserInstructions}";
                await AddMemory(text, existingAgent.Guid.ToString(), "Agent", memoryTags);

                return await Update(existingAgent);
            }
        }

        private async Task<AgentDefinition> Create(AgentDefinitionDto agentDefinitionDto)
        {
            var sql = @"INSERT INTO AgentDefinitions (Guid, AgentName, Instructions, UserInstructions,  ModelName, Workspace, Tools, IsFavorite)
                       VALUES (@Guid, @AgentName, @Instructions, @UserInstructions, @ModelName, @Workspace, @Tools, @IsFavorite)";

            var agentDefinition = new AgentDefinition();
            agentDefinition.Guid = Guid.NewGuid();
            _mapper.Map(agentDefinitionDto, agentDefinition); // Map the DTO to the entity
            await _dbConnection.ExecuteAsync(sql, agentDefinition);
            return agentDefinition;
        }

        private async Task<AgentDefinition> Update(AgentDefinition agentDefinition)
        {
            var sql = @"UPDATE AgentDefinitions
                       SET Instructions = @Instructions,
                            UserInstructions = @UserInstructions,
                            ModelName = @ModelName,
                            Workspace = @Workspace,
                            Tools = @Tools,
                            IsFavorite = @IsFavorite
                       WHERE AgentName = @AgentName";

            await _dbConnection.ExecuteAsync(sql, agentDefinition);
            return agentDefinition;
        }

        public async Task<bool> DeleteByAgentName(string agentName)
        {
            var agent = await GetByAgentName(agentName);
            if (agent == null)
            {
                return false;
            }

            await DeleteSingleMemory(agent.Guid.ToString(), "Agent");
            var sql = "DELETE FROM AgentDefinitions WHERE AgentName = @AgentName";
            var rowsAffected = await _dbConnection.ExecuteAsync(sql, new { AgentName = agentName });
            return rowsAffected > 0;
        }

        public async Task SyncToMemory()
        {
            var sql = "SELECT * FROM AgentDefinitions";
            var agentDefinitions = await _dbConnection.QueryAsync<AgentDefinition>(sql);
            foreach (var agent in agentDefinitions)
            {
                var memoryTags = new List<MemoryTag>
                {
                    new MemoryTag { Name = "WorkspaceName", Value = agent.Workspace }
                };
                var text = $"AgentName: {agent.AgentName} \nAgentDescription: {agent.UserInstructions}";
                await AddMemory(text, agent.Guid.ToString(), "Agent", memoryTags);
            }
        }
        public async Task<string> AddMemory(string text, string id, string index, List<MemoryTag> tags = null)
        {
            var tag = new TagCollection();
            if (tags != null)
            {
                foreach (var memoryTag in tags)
                {
                    tag.Add(memoryTag.Name, memoryTag.Value);
                }
            }
            var res = await _memory.ImportTextAsync(text, id, tags: tag, index: index);

            return res;
        }

        public async Task DeleteSingleMemory(string id, string index)
        {
            await _memory.DeleteDocumentAsync(id, index);
        }

        public async Task<int> TotalAgentCountInWorkspace(string workspace)
        {
            var sql = "SELECT COUNT(*) FROM AgentDefinitions WHERE Workspace = @Workspace";
            return await _dbConnection.ExecuteScalarAsync<int>(sql, new { Workspace = workspace });
        }

        public async Task<bool> ToggleFavorite(string agentName)
        {
            var agent = await GetByAgentName(agentName);

            if (agent == null)
            {
                throw new Exception("Could not find Agent");
            }

            var newFavoriteStatus = !agent.IsFavorite;
            var updateQuery = "UPDATE AgentDefinitions SET IsFavorite = @IsFavorite WHERE AgentName = @AgentName";
            await _dbConnection.ExecuteAsync(updateQuery, new { IsFavorite = newFavoriteStatus, AgentName = agentName });

            return newFavoriteStatus;
        }

        public async Task<IEnumerable<AgentDefinition>> GetFavorites()
        {
            var sql = "SELECT * FROM AgentDefinitions WHERE IsFavorite = 1 ORDER BY AgentName";
            return await _dbConnection.QueryAsync<AgentDefinition>(sql);
        }

    }
}
