{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\workspacecategoryplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\workspacecategoryplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\kernelmemoryplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\kernelmemoryplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\projectcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\projectcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\projectmemorycontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\projectmemorycontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\projectmemory.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\projectmemory.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\agentchatcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\agentchatcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\agentchathistoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\agentchathistoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\iagentchathistoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\iagentchathistoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\sqlgeneratorplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\sqlgeneratorplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\projectapp.infrastructure.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\projectapp.infrastructure.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\aiagentfactory.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\aiagentfactory.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\agentdefinition.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\agentdefinition.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\filerepositoy.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\filerepositoy.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\filecontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\filecontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\agentchatresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\agentchatresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\navigationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\navigationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\agentdefinitioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\agentdefinitioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\plugincontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\plugincontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\aicontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\aicontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\chatcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\chatcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\useraccountrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\useraccountrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\infrastructureserviceregistration.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\infrastructureserviceregistration.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\documentplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\documentplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\workspacerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\workspacerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\workspacecontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\workspacecontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\imemoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\imemoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\agentdefinitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\agentdefinitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\chatmessagedto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\chatmessagedto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\chatrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\chatrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\iagentdefinitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\iagentdefinitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\agentchatrequestdto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\agentchatrequestdto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\filedescriptionplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\filedescriptionplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\filedto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\filedto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\customerplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\customerplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\lightsplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\lightsplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\userskillmatchplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\userskillmatchplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\worksapceagentchooserplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\worksapceagentchooserplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\pluginrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\pluginrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\plugindto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\plugindto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\plugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\plugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\services\\chatsourceservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\services\\chatsourceservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\profiles\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\profiles\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\chatmessage.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\chatmessage.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\emailcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\emailcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\services\\emailservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\services\\emailservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\sqlconnectioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\sqlconnectioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\responsemessage.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\responsemessage.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\docsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\docsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\ichatrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\ichatrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\ifilerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\ifilerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\agentdefinitiondto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\agentdefinitiondto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\apicredentialscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\apicredentialscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\modeldetailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\modeldetailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\imodeldetailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\imodeldetailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\modeldetailscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\modeldetailscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\modeldetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\modeldetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\modeldetailsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\modeldetailsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\iapicredentailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\iapicredentailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\apicredentials.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\apicredentials.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\Database\\UpdateSchemaForCustomModels.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Database\\UpdateSchemaForCustomModels.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\apicredentialsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\apicredentialsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\docsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\docsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\docscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\docscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\idocsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\idocsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\promptlibrarycontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\promptlibrarycontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\promptlibraryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\promptlibraryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\promptlibrary.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\promptlibrary.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\aidto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\aidto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\projectapp.webapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\projectapp.webapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\services\\dailyinsightservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\services\\dailyinsightservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\dailyinsightagentrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\dailyinsightagentrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\idailyinsightagentrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\idailyinsightagentrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.webapi\\controllers\\dailyinsightcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3CEF-4510-9EF0-675FFF8A15C0}|ProjectApp.WebApi\\ProjectApp.WebApi.csproj|solutionrelative:projectapp.webapi\\controllers\\dailyinsightcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\dailyinsightagent.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\dailyinsightagent.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\dailyinsightagentdto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\dailyinsightagentdto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\extractemailfromaccessor.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\extractemailfromaccessor.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\iextractemailfromaccessor.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\iextractemailfromaccessor.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\iworkspacerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\iworkspacerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\ipluginrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\ipluginrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\dtos\\promptlibrarydto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\dtos\\promptlibrarydto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\projectcategory.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\projectcategory.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\ipromptlibraryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\ipromptlibraryrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\services\\openapipluginservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\services\\openapipluginservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\services\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\services\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\services\\loggingfilter.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\services\\loggingfilter.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\repositories\\sqlconnectionrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\repositories\\sqlconnectionrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\models\\sqlconnectioninfo.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\models\\sqlconnectioninfo.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.core\\repositories\\isqlconnectionrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9FA7BF3D-1E98-44D6-98C3-A8BE8AEC5EDB}|ProjectApp.Core\\ProjectApp.Core.csproj|solutionrelative:projectapp.core\\repositories\\isqlconnectionrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\project-app\\webapi\\projectapp.infrastructure\\aiagents\\tools\\testfilegeneratorplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C08BCAF2-19AD-4618-9BDF-76FA40412D4E}|ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj|solutionrelative:projectapp.infrastructure\\aiagents\\tools\\testfilegeneratorplugin.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 29, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{004be353-6879-467c-9d1e-9ac23cdf6d49}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:138:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:141:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:156:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:153:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:152:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:151:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:150:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:149:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:148:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:147:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:146:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:145:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:144:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:143:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:142:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:136:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:154:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:155:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:2132056560:0:{83107a3e-496a-485e-b455-16ddb978e55e}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:137:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "AiService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AiService.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AiService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AiService.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AiService.cs", "ViewState": "AgIAAPQBAAAAAAAAAAAgwAYCAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T09:21:52.164Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ProjectMemoryController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ProjectMemoryController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\ProjectMemoryController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ProjectMemoryController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\ProjectMemoryController.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAB4AAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T17:02:37.193Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ProjectController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ProjectController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\ProjectController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ProjectController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\ProjectController.cs", "ViewState": "AgIAADsAAAAAAAAAAAASwEgAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-18T10:24:03.167Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "KernelMemoryPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\KernelMemoryPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\KernelMemoryPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\KernelMemoryPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\KernelMemoryPlugin.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAACIAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-06T14:39:21.383Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "WorkspaceCategoryPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\WorkspaceCategoryPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\WorkspaceCategoryPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\WorkspaceCategoryPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\WorkspaceCategoryPlugin.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAxwCcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T10:47:05.231Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "AgentChatController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\AgentChatController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\AgentChatController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\AgentChatController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\AgentChatController.cs", "ViewState": "AgIAALMCAAAAAAAAAAAWwMoCAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T12:48:23.864Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "IAgentChatHistoryRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IAgentChatHistoryRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IAgentChatHistoryRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IAgentChatHistoryRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IAgentChatHistoryRepository.cs", "ViewState": "AgIAAA0AAAAAAAAAAAA5wB4AAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T19:38:22.638Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "SqlGeneratorPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\SqlGeneratorPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\SqlGeneratorPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\SqlGeneratorPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\SqlGeneratorPlugin.cs", "ViewState": "AgIAADUAAAAAAAAAAAAgwEIAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T14:13:27.589Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ProjectMemory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ProjectMemory.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\ProjectMemory.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ProjectMemory.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\ProjectMemory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-02T02:30:16.985Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "AIAgentFactory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\AIAgentFactory.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\AIAgentFactory.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\AIAgentFactory.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\AIAgentFactory.cs", "ViewState": "AgIAACkAAAAAAAAAAAAcwDsAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-06T14:39:21.399Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "AgentChatHistoryRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\AgentChatHistoryRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\AgentChatHistoryRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\AgentChatHistoryRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\AgentChatHistoryRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwBwAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T12:18:45.212Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "AgentDefinition.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\AgentDefinition.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\AgentDefinition.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\AgentDefinition.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\AgentDefinition.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T15:16:17.078Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "FileController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\FileController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\FileController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\FileController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\FileController.cs", "ViewState": "AgIAACYAAAAAAAAAAAAwwDkAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T12:45:18.025Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "FileRepositoy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\FileRepositoy.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\FileRepositoy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\FileRepositoy.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\FileRepositoy.cs", "ViewState": "AgIAAGYAAAAAAAAAAADwv84AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-25T10:29:49.493Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "ProjectApp.Infrastructure.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj", "RelativeToolTip": "ProjectApp.Infrastructure\\ProjectApp.Infrastructure.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAiwBAAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-20T09:02:40.424Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "AgentDefinitionController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\AgentDefinitionController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\AgentDefinitionController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\AgentDefinitionController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\AgentDefinitionController.cs", "ViewState": "AgIAAF4AAAAAAAAAAAAAAGgAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-16T14:34:18.151Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "PluginController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\PluginController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\PluginController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\PluginController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\PluginController.cs", "ViewState": "AgIAAJsAAAAAAAAAAAAYwHoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T20:16:31.105Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "NavigationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\NavigationController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\NavigationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\NavigationController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\NavigationController.cs", "ViewState": "AgIAAPYAAAAAAAAAAAAowAsBAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T09:24:14.772Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "AgentChatResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\AgentChatResponse.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\AgentChatResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\AgentChatResponse.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\AgentChatResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T18:03:23.95Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "ChatController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ChatController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\ChatController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ChatController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\ChatController.cs", "ViewState": "AgIAACgAAAAAAAAAAAAqwDkAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T20:24:03.727Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "AiController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\AiController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\AiController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\AiController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\AiController.cs", "ViewState": "AgIAAMMAAAAAAAAAAAAewMgAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T15:16:33.271Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "InfrastructureServiceRegistration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\InfrastructureServiceRegistration.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\InfrastructureServiceRegistration.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\InfrastructureServiceRegistration.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\InfrastructureServiceRegistration.cs", "ViewState": "AgIAACEAAAAAAAAAAAAEwC0AAABfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T18:05:48.268Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\appsettings.Development.json", "RelativeDocumentMoniker": "ProjectApp.WebApi\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\appsettings.Development.json", "RelativeToolTip": "ProjectApp.WebApi\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-04-25T09:20:07.483Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Program.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Program.cs", "RelativeToolTip": "ProjectApp.WebApi\\Program.cs", "ViewState": "AgIAAGcAAAAAAAAAAADwv30AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-18T08:52:24.502Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "UserAccountRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\UserAccountRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\UserAccountRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\UserAccountRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\UserAccountRepository.cs", "ViewState": "AgIAAHUAAAAAAAAAAAAmwHUAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T20:22:06.533Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "DocumentPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\DocumentPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\DocumentPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\DocumentPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\DocumentPlugin.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-06T14:39:21.367Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\appsettings.json", "RelativeDocumentMoniker": "ProjectApp.WebApi\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\appsettings.json", "RelativeToolTip": "ProjectApp.WebApi\\appsettings.json", "ViewState": "AgIAADAAAAAAAAAAAAAAAEAAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-04-24T14:12:36.406Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "IMemoryRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IMemoryRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IMemoryRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IMemoryRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IMemoryRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T17:37:55.595Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "WorkspaceRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\WorkspaceRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\WorkspaceRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\WorkspaceRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\WorkspaceRepository.cs", "ViewState": "AgIAAG8AAAAAAAAAAAAhwIQAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T10:47:11.386Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "WorkspaceController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\WorkspaceController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\WorkspaceController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\WorkspaceController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\WorkspaceController.cs", "ViewState": "AgIAAFgAAAAAAAAAAAAcwGkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-17T03:48:50.867Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "ChatRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\ChatRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\ChatRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\ChatRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\ChatRepository.cs", "ViewState": "AgIAADUAAAAAAAAAAAAowFIAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-06T07:29:38.251Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "AgentDefinitionRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\AgentDefinitionRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\AgentDefinitionRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\AgentDefinitionRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\AgentDefinitionRepository.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABYAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-02T02:44:03.414Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "ChatMessageDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ChatMessageDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\ChatMessageDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ChatMessageDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\ChatMessageDto.cs", "ViewState": "AgIAABAAAAAAAAAAAAA6wCMAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T08:05:26.697Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "IAgentDefinitionRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IAgentDefinitionRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IAgentDefinitionRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IAgentDefinitionRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IAgentDefinitionRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAIwBEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T06:04:52.068Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "AgentChatRequestDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\AgentChatRequestDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\AgentChatRequestDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\AgentChatRequestDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\AgentChatRequestDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T10:03:31.054Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "FileDescriptionPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\FileDescriptionPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\FileDescriptionPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\FileDescriptionPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\FileDescriptionPlugin.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-06T14:39:21.383Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "FileDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\FileDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\FileDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\FileDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\FileDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T02:18:33.2Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "CustomerPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\CustomerPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\CustomerPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\CustomerPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\CustomerPlugin.cs", "ViewState": "AgIAADUAAAAAAAAAAAAIwEoAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-06T13:31:14.314Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "LightsPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\LightsPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\LightsPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\LightsPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\LightsPlugin.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAowBEAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-06T15:45:04.707Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "UserSkillMatchPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\UserSkillMatchPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\UserSkillMatchPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\UserSkillMatchPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\UserSkillMatchPlugin.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T01:38:17.274Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "WorksapceAgentChooserPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\WorksapceAgentChooserPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\WorksapceAgentChooserPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\WorksapceAgentChooserPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\WorksapceAgentChooserPlugin.cs", "ViewState": "AgIAABAAAAAAAAAAAAA8wB8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-18T08:53:40.904Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "MappingProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Profiles\\MappingProfile.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Profiles\\MappingProfile.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Profiles\\MappingProfile.cs", "RelativeToolTip": "ProjectApp.Core\\Profiles\\MappingProfile.cs", "ViewState": "AgIAABkAAAAAAAAAAAAAADMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T03:47:42.195Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "PluginRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\PluginRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\PluginRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\PluginRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\PluginRepository.cs", "ViewState": "AgIAAJkAAAAAAAAAAAAkwK4AAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T11:55:33.257Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 88, "Title": "LoggingFilter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\LoggingFilter.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Services\\LoggingFilter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\LoggingFilter.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Services\\LoggingFilter.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAQwBoAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T08:09:56.68Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 44, "Title": "ChatSourceService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\ChatSourceService.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Services\\ChatSourceService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\ChatSourceService.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Services\\ChatSourceService.cs", "ViewState": "AgIAAAkAAAAAAAAAAIAwwBkAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T09:00:19.419Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "Plugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\Plugin.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\Plugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\Plugin.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\Plugin.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-10T05:05:09.653Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "PluginDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\PluginDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\PluginDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\PluginDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\PluginDto.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAjwA8AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-10T06:20:31.671Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "ChatMessage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\ChatMessage.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\ChatMessage.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\ChatMessage.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\ChatMessage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T02:59:33.828Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "EmailService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\EmailService.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Services\\EmailService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\EmailService.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Services\\EmailService.cs", "ViewState": "AgIAABsAAAAAAAAAAAAUwCMAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T04:55:15.165Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "DocsDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\DocsDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\DocsDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\DocsDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\DocsDto.cs", "ViewState": "AgIAABoAAAAAAAAAAAAswCMAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-25T09:34:22.718Z"}, {"$type": "Document", "DocumentIndex": 89, "Title": "SqlConnectionRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\SqlConnectionRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\SqlConnectionRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\SqlConnectionRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\SqlConnectionRepository.cs", "ViewState": "AgIAACkAAAAAAAAAAAAqwDQAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T04:28:57.53Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "EmailController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\EmailController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\EmailController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\EmailController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\EmailController.cs", "ViewState": "AgIAACAAAAAAAAAAAAAqwDAAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T04:53:25.013Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "SqlConnectionController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\SqlConnectionController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\SqlConnectionController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\SqlConnectionController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\SqlConnectionController.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwB0AAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T13:10:09.65Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "ResponseMessage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ResponseMessage.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\ResponseMessage.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ResponseMessage.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\ResponseMessage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAIAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T06:56:04.445Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "SqlConnectionInfo.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\SqlConnectionInfo.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\SqlConnectionInfo.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\SqlConnectionInfo.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\SqlConnectionInfo.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAkwBMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T13:06:47.384Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "ISqlConnectionRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\ISqlConnectionRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\ISqlConnectionRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\ISqlConnectionRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\ISqlConnectionRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAoAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T13:07:33.493Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "IChatRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IChatRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IChatRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IChatRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IChatRepository.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAAAAcAAACtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T06:39:53.318Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "TestFileGeneratorPlugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\TestFileGeneratorPlugin.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\AIAgents\\Tools\\TestFileGeneratorPlugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\AIAgents\\Tools\\TestFileGeneratorPlugin.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\AIAgents\\Tools\\TestFileGeneratorPlugin.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAowEAAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T07:31:11.756Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 53, "Title": "IFileRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IFileRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IFileRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IFileRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IFileRepository.cs", "ViewState": "AgIAAA0AAAAAAAAAAIAzwBcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T03:48:58.984Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "AgentDefinitionDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\AgentDefinitionDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\AgentDefinitionDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\AgentDefinitionDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\AgentDefinitionDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAgAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-02T02:46:16.772Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "ModelDetailsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\ModelDetailsRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\ModelDetailsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\ModelDetailsRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\ModelDetailsRepository.cs", "ViewState": "AgIAANwAAAAAAAAAAAAawOgAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-09T01:48:49.437Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "ApiCredentialsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ApiCredentialsController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\ApiCredentialsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ApiCredentialsController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\ApiCredentialsController.cs", "ViewState": "AgIAABcBAAAAAAAAAAAUwCMBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-06T07:28:41.513Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 57, "Title": "IModelDetailsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IModelDetailsRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IModelDetailsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IModelDetailsRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IModelDetailsRepository.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAAAA0AAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-02T02:56:17.413Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "ModelDetailsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ModelDetailsController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\ModelDetailsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\ModelDetailsController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\ModelDetailsController.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAYwBgAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-08T09:54:53.297Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "ModelDetailsDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ModelDetailsDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\ModelDetailsDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ModelDetailsDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\ModelDetailsDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAsAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-02T02:52:57.141Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "ModelDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\ModelDetails.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\ModelDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\ModelDetails.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\ModelDetails.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAuwA0AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T14:44:24.218Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "UpdateSchemaForCustomModels.sql", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\Database\\UpdateSchemaForCustomModels.sql", "RelativeDocumentMoniker": "Database\\UpdateSchemaForCustomModels.sql", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\Database\\UpdateSchemaForCustomModels.sql", "RelativeToolTip": "Database\\UpdateSchemaForCustomModels.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-05-08T09:59:17.799Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "IApiCredentailsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IApiCredentailsRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IApiCredentailsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IApiCredentailsRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IApiCredentailsRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-08T10:19:30.314Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "ApiCredentials.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\ApiCredentials.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\ApiCredentials.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\ApiCredentials.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\ApiCredentials.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAsAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-07T07:58:50.905Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "ApiCredentialsDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ApiCredentialsDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\ApiCredentialsDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\ApiCredentialsDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\ApiCredentialsDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-07T07:58:35.139Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "DocsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\DocsRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\DocsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\DocsRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\DocsRepository.cs", "ViewState": "AgIAAGkAAAAABAAAAEAtwH8AAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-02T14:19:00.583Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 69, "Title": "PromptLibraryRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\PromptLibraryRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\PromptLibraryRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\PromptLibraryRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\PromptLibraryRepository.cs", "ViewState": "AgIAAGEAAAAAAAAAAAAlwFYAAABaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T12:48:20.055Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "DocsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\DocsController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\DocsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\DocsController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\DocsController.cs", "ViewState": "AgIAACUAAAAAAAAAAIA1wDUAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T09:47:28.732Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 67, "Title": "IDocsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IDocsRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IDocsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IDocsRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IDocsRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAgAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-29T12:20:28.739Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "PromptLibraryController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\PromptLibraryController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\PromptLibraryController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\PromptLibraryController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\PromptLibraryController.cs", "ViewState": "AgIAABUAAAAAAAAAAAAowDAAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T13:31:40.974Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "PromptLibrary.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\PromptLibrary.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\PromptLibrary.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\PromptLibrary.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\PromptLibrary.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-31T14:00:36.745Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "AiDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\AiDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\AiDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\AiDto.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\AiDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-11T08:55:01.955Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "DailyInsightService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\DailyInsightService.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Services\\DailyInsightService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\DailyInsightService.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Services\\DailyInsightService.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAYwGMAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-20T04:05:25.087Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "ProjectApp.WebApi.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\ProjectApp.WebApi.csproj", "RelativeDocumentMoniker": "ProjectApp.WebApi\\ProjectApp.WebApi.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\ProjectApp.WebApi.csproj", "RelativeToolTip": "ProjectApp.WebApi\\ProjectApp.WebApi.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-04-23T11:25:48.145Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "DailyInsightAgentRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\DailyInsightAgentRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\DailyInsightAgentRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\DailyInsightAgentRepository.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\DailyInsightAgentRepository.cs", "ViewState": "AgIAAEIAAAAAAAAAAAAmwCcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-20T04:04:42.115Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "IDailyInsightAgentRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IDailyInsightAgentRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IDailyInsightAgentRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IDailyInsightAgentRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IDailyInsightAgentRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-20T04:04:00.537Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "DailyInsightAgentDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\DailyInsightAgentDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\DailyInsightAgentDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\DailyInsightAgentDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\DailyInsightAgentDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAwAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-20T04:03:11.125Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "DailyInsightController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\DailyInsightController.cs", "RelativeDocumentMoniker": "ProjectApp.WebApi\\Controllers\\DailyInsightController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\Controllers\\DailyInsightController.cs", "RelativeToolTip": "ProjectApp.WebApi\\Controllers\\DailyInsightController.cs", "ViewState": "AgIAAGYAAAAAAAAAAAAcwHUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-20T04:05:57.066Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "DailyInsightAgent.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\DailyInsightAgent.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\DailyInsightAgent.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\DailyInsightAgent.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\DailyInsightAgent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAsAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-20T04:03:34.171Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "IExtractEmailFromAccessor.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IExtractEmailFromAccessor.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IExtractEmailFromAccessor.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IExtractEmailFromAccessor.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IExtractEmailFromAccessor.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAmwAwAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-17T05:12:57.338Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "ExtractEmailFromAccessor.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\ExtractEmailFromAccessor.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Repositories\\ExtractEmailFromAccessor.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Repositories\\ExtractEmailFromAccessor.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Repositories\\ExtractEmailFromAccessor.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAB0AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T12:59:53.384Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "IWorkspaceRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IWorkspaceRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IWorkspaceRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IWorkspaceRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IWorkspaceRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAwAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-02T02:07:36.484Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "IPluginRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IPluginRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IPluginRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IPluginRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IPluginRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T14:08:51.936Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "OpenApiPluginService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\OpenApiPluginService.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Services\\OpenApiPluginService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\OpenApiPluginService.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Services\\OpenApiPluginService.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAjwFAAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T11:52:06.057Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "AuthService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\AuthService.cs", "RelativeDocumentMoniker": "ProjectApp.Infrastructure\\Services\\AuthService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Infrastructure\\Services\\AuthService.cs", "RelativeToolTip": "ProjectApp.Infrastructure\\Services\\AuthService.cs", "ViewState": "AgIAADgAAAAAAAAAAEAjwFkAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-04T03:37:46.258Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "PromptLibraryDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\PromptLibraryDto.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Dtos\\PromptLibraryDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Dtos\\PromptLibraryDto.cs", "RelativeToolTip": "ProjectApp.Core\\Dtos\\PromptLibraryDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-31T13:59:41.11Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "ProjectCategory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\ProjectCategory.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Models\\ProjectCategory.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Models\\ProjectCategory.cs", "RelativeToolTip": "ProjectApp.Core\\Models\\ProjectCategory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAgAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T01:40:18.708Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "IPromptLibraryRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IPromptLibraryRepository.cs", "RelativeDocumentMoniker": "ProjectApp.Core\\Repositories\\IPromptLibraryRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.Core\\Repositories\\IPromptLibraryRepository.cs", "RelativeToolTip": "ProjectApp.Core\\Repositories\\IPromptLibraryRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T09:51:11.295Z"}]}]}]}