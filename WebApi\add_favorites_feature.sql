-- Add IsFavorite column to Workspaces table
-- This script adds the favorites functionality to both Workspaces and AgentDefinitions tables

-- Add IsFavorite column to Workspaces table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Workspaces]') AND name = 'IsFavorite')
BEGIN
    ALTER TABLE Workspaces ADD IsFavorite BIT NOT NULL DEFAULT 0;
    PRINT 'Added IsFavorite column to Workspaces table';
END
ELSE
BEGIN
    PRINT 'IsFavorite column already exists in Workspaces table';
END

-- Add IsFavorite column to AgentDefinitions table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[AgentDefinitions]') AND name = 'IsFavorite')
BEGIN
    ALTER TABLE AgentDefinitions ADD IsFavorite BIT NOT NULL DEFAULT 0;
    PRINT 'Added IsFavorite column to AgentDefinitions table';
END
ELSE
BEGIN
    PRINT 'IsFavorite column already exists in AgentDefinitions table';
END

-- Set default favorites for "My Notes" and "General Chat" workspaces
UPDATE Workspaces 
SET IsFavorite = 1 
WHERE Title IN ('My Notes', 'General Chat');

PRINT 'Set default favorites for My Notes and General Chat workspaces';

-- Display current state
SELECT 'Workspaces with Favorites' as TableInfo;
SELECT Id, Title, IsFavorite FROM Workspaces WHERE IsFavorite = 1;

SELECT 'AgentDefinitions Table Structure' as TableInfo;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'AgentDefinitions' 
AND COLUMN_NAME IN ('AgentName', 'IsFavorite')
ORDER BY ORDINAL_POSITION;
