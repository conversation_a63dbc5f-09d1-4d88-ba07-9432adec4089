﻿using ProjectApp.Core.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IProjectMemoryRepository
    {
        Task<ProjectMemory> SaveProjectMemoryAsync(ProjectMemory projectMemory);
        Task<ProjectMemory> GetProjectMemoryByIdAsync(int id);
        Task<List<ProjectMemory>> GetAllProjectMemoriesAsync(string workspace = null);
        Task<bool> DeleteProjectMemoryAsync(int id);
    }
}
