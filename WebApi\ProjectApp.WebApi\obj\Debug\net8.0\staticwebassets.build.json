{"Version": 1, "Hash": "FtmIPF2qdwuN88z3v0N4OQYTsexCZFklRIRqG0gGtog=", "Source": "ProjectApp.WebApi", "BasePath": "_content/ProjectApp.WebApi", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ProjectApp.WebApi\\wwwroot", "Source": "ProjectApp.WebApi", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\.gitkeep", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "#[.{fingerprint}]?.gitkeep", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\.gitkeep", "FileLength": 0, "LastWriteTime": "2025-05-25T10:59:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\agent-chat-test.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "agent-chat-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2m651078tc", "Integrity": "usFFnQU4D9cPkeG1dil1t3lWgs6t+Ap1Wi9c+JkahO4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\agent-chat-test.html", "FileLength": 10511, "LastWriteTime": "2025-05-28T06:39:11+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\index.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "61b8v8xsty", "Integrity": "JmclD8wVnxXPeDAYYlY6ZkoWbccn5/qi8hPD//2KSC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 10912, "LastWriteTime": "2025-06-20T09:47:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugin-details.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "plugin-details#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3az2yg0702", "Integrity": "G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\plugin-details.html", "FileLength": 9832, "LastWriteTime": "2025-04-15T03:14:27+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugins.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "plugins#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "367d1cpt86", "Integrity": "dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\plugins.html", "FileLength": 23350, "LastWriteTime": "2025-04-15T03:14:23+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo-enhanced.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "process-framework-demo-enhanced#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "17i7mq3p3o", "Integrity": "3UXSomXtIDgOg5LUfrjLpnuXO1mXbH2AFN/xLtR+V+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\process-framework-demo-enhanced.html", "FileLength": 28458, "LastWriteTime": "2025-06-20T09:47:10+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "process-framework-demo#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lys8hslai6", "Integrity": "nbXoqtXOyIhly1wZB3ZJj9qs3SDlD9HfqQnr+wJYvKA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\process-framework-demo.html", "FileLength": 11201, "LastWriteTime": "2025-06-20T09:45:30+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\apexmakerclub5.png", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/apexmakerclub5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d5944pppt6", "Integrity": "r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\apexmakerclub5.png", "FileLength": 57189, "LastWriteTime": "2025-06-04T13:20:34+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpeg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_1#[.{fingerprint}]?.jpeg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_1.jpeg", "FileLength": 56443, "LastWriteTime": "2025-05-19T09:48:39+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_1.jpg", "FileLength": 56443, "LastWriteTime": "2025-03-31T11:23:58+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_2.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_2.jpg", "FileLength": 56443, "LastWriteTime": "2025-04-01T03:30:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_3.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_3.jpg", "FileLength": 56443, "LastWriteTime": "2025-04-01T03:38:04+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_4.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_4#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_4.jpg", "FileLength": 56443, "LastWriteTime": "2025-05-19T09:43:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image.jpg", "FileLength": 56443, "LastWriteTime": "2025-03-31T11:23:23+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-16T09:34:54+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T01:54:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T03:02:09+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T03:49:10+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-19T08:51:27+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "FileLength": 20565, "LastWriteTime": "2025-05-16T04:28:27+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\landingPage.png", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/landingPage#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "929y9semqo", "Integrity": "YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\landingPage.png", "FileLength": 292001, "LastWriteTime": "2025-03-24T19:56:44+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/maxresdefault_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x27hb5tlzi", "Integrity": "GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\maxresdefault_1.jpg", "FileLength": 99558, "LastWriteTime": "2025-05-19T09:54:04+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/maxresdefault#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x27hb5tlzi", "Integrity": "GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\maxresdefault.jpg", "FileLength": 99558, "LastWriteTime": "2025-05-19T09:53:51+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.csv", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/name#[.{fingerprint}]?.csv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gze1rkoz4g", "Integrity": "5PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\name.csv", "FileLength": 32, "LastWriteTime": "2025-06-04T12:59:12+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.xlsx", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/name#[.{fingerprint}]?.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vskwaa03f5", "Integrity": "T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\name.xlsx", "FileLength": 4748, "LastWriteTime": "2025-06-04T12:57:22+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:12:08+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_2.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:13:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_3.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_3#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_3.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:26:31+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1_4.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:29:40+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/receipt_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\receipt_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T11:11:15+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_amardeep.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt_amardeep#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt_amardeep.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T05:33:15+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt_shyam_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt_shyam_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T10:55:37+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt_shyam#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt_shyam.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T10:39:47+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "FileLength": 20386, "LastWriteTime": "2025-06-05T04:48:17+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T04:23:29+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_3#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T04:26:51+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T06:57:45+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-19T02:39:32+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-19T03:35:23+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_7#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-19T05:09:55+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-25T11:12:29+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt.pdf", "FileLength": 20386, "LastWriteTime": "2025-05-16T04:15:39+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/robot leg_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hk1hg0l2za", "Integrity": "MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\robot leg_1.jpg", "FileLength": 47187, "LastWriteTime": "2025-04-03T01:18:44+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/robot leg#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hk1hg0l2za", "Integrity": "MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\robot leg.jpg", "FileLength": 47187, "LastWriteTime": "2025-04-03T01:14:49+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_1.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/sql_1#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o2p4fx5oup", "Integrity": "8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\sql_1.txt", "FileLength": 1657, "LastWriteTime": "2025-06-23T20:49:15+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_2.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/sql_2#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o2p4fx5oup", "Integrity": "8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\sql_2.txt", "FileLength": 1657, "LastWriteTime": "2025-06-23T20:51:58+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/sql#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o2p4fx5oup", "Integrity": "8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\sql.txt", "FileLength": 1657, "LastWriteTime": "2025-06-23T20:45:47+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.docx", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/test#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lacrko49p0", "Integrity": "/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\test.docx", "FileLength": 6507, "LastWriteTime": "2025-06-04T13:01:56+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.txt", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/test#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "54emubzsk1", "Integrity": "C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB+8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\test.txt", "FileLength": 38, "LastWriteTime": "2025-06-04T13:03:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\text.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/text#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4rhg4rlloj", "Integrity": "IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\text.jpg", "FileLength": 109894, "LastWriteTime": "2025-06-04T13:28:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe_1.jpg", "FileLength": 25704, "LastWriteTime": "2025-05-01T12:44:25+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_2.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe_2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe_2.jpg", "FileLength": 25704, "LastWriteTime": "2025-05-01T12:46:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe.jpg", "FileLength": 25704, "LastWriteTime": "2025-05-01T12:30:29+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow-demo-enhanced.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "workflow-demo-enhanced#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "7ARxMipZYjf/8x4gRKc+2lGjbPHka8mSXbQF9+li3pE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\workflow-demo-enhanced.html", "FileLength": 27351, "LastWriteTime": "2025-06-23T08:23:00+00:00"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "workflow#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\workflow.html", "FileLength": 0, "LastWriteTime": "2025-06-19T13:30:34+00:00"}], "Endpoints": [{"Route": ".5ipweew5fc.gitkeep", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\.gitkeep", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 10:59:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": ".gitkeep"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": ".gitkeep", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\.gitkeep", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 10:59:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "agent-chat-test.2m651078tc.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\agent-chat-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10511"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"usFFnQU4D9cPkeG1dil1t3lWgs6t+Ap1Wi9c+JkahO4=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 06:39:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2m651078tc"}, {"Name": "label", "Value": "agent-chat-test.html"}, {"Name": "integrity", "Value": "sha256-usFFnQU4D9cPkeG1dil1t3lWgs6t+Ap1Wi9c+JkahO4="}]}, {"Route": "agent-chat-test.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\agent-chat-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10511"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"usFFnQU4D9cPkeG1dil1t3lWgs6t+Ap1Wi9c+JkahO4=\""}, {"Name": "Last-Modified", "Value": "Wed, 28 May 2025 06:39:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-usFFnQU4D9cPkeG1dil1t3lWgs6t+Ap1Wi9c+JkahO4="}]}, {"Route": "index.61b8v8xsty.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10912"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"JmclD8wVnxXPeDAYYlY6ZkoWbccn5/qi8hPD//2KSC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 09:47:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61b8v8xsty"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-JmclD8wVnxXPeDAYYlY6ZkoWbccn5/qi8hPD//2KSC0="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10912"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"JmclD8wVnxXPeDAYYlY6ZkoWbccn5/qi8hPD//2KSC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 09:47:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JmclD8wVnxXPeDAYYlY6ZkoWbccn5/qi8hPD//2KSC0="}]}, {"Route": "plugin-details.3az2yg0702.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugin-details.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9832"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Apr 2025 03:14:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3az2yg0702"}, {"Name": "label", "Value": "plugin-details.html"}, {"Name": "integrity", "Value": "sha256-G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I="}]}, {"Route": "plugin-details.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugin-details.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9832"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Apr 2025 03:14:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I="}]}, {"Route": "plugins.367d1cpt86.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugins.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23350"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Apr 2025 03:14:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "367d1cpt86"}, {"Name": "label", "Value": "plugins.html"}, {"Name": "integrity", "Value": "sha256-dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE="}]}, {"Route": "plugins.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugins.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23350"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Apr 2025 03:14:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE="}]}, {"Route": "process-framework-demo-enhanced.17i7mq3p3o.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo-enhanced.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28458"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3UXSomXtIDgOg5LUfrjLpnuXO1mXbH2AFN/xLtR+V+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 09:47:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "17i7mq3p3o"}, {"Name": "label", "Value": "process-framework-demo-enhanced.html"}, {"Name": "integrity", "Value": "sha256-3UXSomXtIDgOg5LUfrjLpnuXO1mXbH2AFN/xLtR+V+s="}]}, {"Route": "process-framework-demo-enhanced.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo-enhanced.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28458"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3UXSomXtIDgOg5LUfrjLpnuXO1mXbH2AFN/xLtR+V+s=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 09:47:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3UXSomXtIDgOg5LUfrjLpnuXO1mXbH2AFN/xLtR+V+s="}]}, {"Route": "process-framework-demo.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11201"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"nbXoqtXOyIhly1wZB3ZJj9qs3SDlD9HfqQnr+wJYvKA=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 09:45:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nbXoqtXOyIhly1wZB3ZJj9qs3SDlD9HfqQnr+wJYvKA="}]}, {"Route": "process-framework-demo.lys8hslai6.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\process-framework-demo.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11201"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"nbXoqtXOyIhly1wZB3ZJj9qs3SDlD9HfqQnr+wJYvKA=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 09:45:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lys8hslai6"}, {"Name": "label", "Value": "process-framework-demo.html"}, {"Name": "integrity", "Value": "sha256-nbXoqtXOyIhly1wZB3ZJj9qs3SDlD9HfqQnr+wJYvKA="}]}, {"Route": "uploads/apexmakerclub5.d5944pppt6.png", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\apexmakerclub5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57189"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 13:20:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d5944pppt6"}, {"Name": "label", "Value": "uploads/apexmakerclub5.png"}, {"Name": "integrity", "Value": "sha256-r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ="}]}, {"Route": "uploads/apexmakerclub5.png", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\apexmakerclub5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "57189"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 13:20:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ="}]}, {"Route": "uploads/image_1.536fkm048a.jpeg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:48:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_1.jpeg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_1.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 31 Mar 2025 11:23:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_1.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_1.jpeg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:48:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_1.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 31 Mar 2025 11:23:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_2.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Apr 2025 03:30:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_2.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_2.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Apr 2025 03:30:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_3.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Apr 2025 03:38:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_3.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Apr 2025 03:38:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_4.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:43:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_4.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_4.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:43:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 31 Mar 2025 11:23:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 31 Mar 2025 11:23:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/Invoice-F66D817D-0001_1.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 09:34:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_1.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_1.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 09:34:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_2.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 01:54:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_2.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_2.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 01:54:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_4.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:02:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_4.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_4.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:02:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_5.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:49:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_5.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_5.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:49:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_6.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:51:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_6.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_6.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:51:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:28:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:28:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/landingPage.929y9semqo.png", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\landingPage.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292001"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 19:56:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "929y9semqo"}, {"Name": "label", "Value": "uploads/landingPage.png"}, {"Name": "integrity", "Value": "sha256-YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo="}]}, {"Route": "uploads/landingPage.png", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\landingPage.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292001"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 19:56:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo="}]}, {"Route": "uploads/maxresdefault_1.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "99558"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:54:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]}, {"Route": "uploads/maxresdefault_1.x27hb5tlzi.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "99558"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:54:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x27hb5tlzi"}, {"Name": "label", "Value": "uploads/maxresdefault_1.jpg"}, {"Name": "integrity", "Value": "sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]}, {"Route": "uploads/maxresdefault.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "99558"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:53:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]}, {"Route": "uploads/maxresdefault.x27hb5tlzi.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "99558"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:53:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x27hb5tlzi"}, {"Name": "label", "Value": "uploads/maxresdefault.jpg"}, {"Name": "integrity", "Value": "sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]}, {"Route": "uploads/name.csv", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.csv", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"5PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 12:59:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ="}]}, {"Route": "uploads/name.gze1rkoz4g.csv", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.csv", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"5PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 12:59:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gze1rkoz4g"}, {"Name": "label", "Value": "uploads/name.csv"}, {"Name": "integrity", "Value": "sha256-5PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ="}]}, {"Route": "uploads/name.vskwaa03f5.xlsx", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.xlsx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4748"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}, {"Name": "ETag", "Value": "\"T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 12:57:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vskwaa03f5"}, {"Name": "label", "Value": "uploads/name.xlsx"}, {"Name": "integrity", "Value": "sha256-T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY="}]}, {"Route": "uploads/name.xlsx", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.xlsx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4748"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}, {"Name": "ETag", "Value": "\"T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 12:57:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY="}]}, {"Route": "uploads/receipt_1_1.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:12:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/receipt_1_1.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:12:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/receipt_1_1.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/receipt_1_2.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:13:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/receipt_1_2.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:13:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/receipt_1_2.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/receipt_1_3.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_3.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:26:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/receipt_1_3.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_3.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:26:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/receipt_1_3.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/receipt_1_4.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:29:40 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/receipt_1_4.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:29:40 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/receipt_1_4.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/receipt_1.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:11:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/receipt_1.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 11:11:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/receipt_1.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt_amardeep.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_amardeep.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 05:33:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt_amardeep.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_amardeep.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 05:33:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt_amardeep.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt_shyam_1.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 10:55:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt_shyam_1.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 10:55:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt_shyam_1.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt_shyam.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 10:39:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt_shyam.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 10:39:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt_shyam.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_1.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 04:48:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_1.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 04:48:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_1.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_2.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:23:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_2.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:23:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_2.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_3.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:26:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_3.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:26:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_3.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_4.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 06:57:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_4.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 06:57:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_4.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_5.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 02:39:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_5.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 02:39:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_5.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_6.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:35:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_6.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:35:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_6.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_7.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 05:09:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_7.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 05:09:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_7.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 11:12:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 11:12:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:15:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:15:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/robot leg_1.hk1hg0l2za.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47187"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Apr 2025 01:18:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hk1hg0l2za"}, {"Name": "label", "Value": "uploads/robot leg_1.jpg"}, {"Name": "integrity", "Value": "sha256-MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI="}]}, {"Route": "uploads/robot leg_1.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47187"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Apr 2025 01:18:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI="}]}, {"Route": "uploads/robot leg.hk1hg0l2za.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47187"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Apr 2025 01:14:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hk1hg0l2za"}, {"Name": "label", "Value": "uploads/robot leg.jpg"}, {"Name": "integrity", "Value": "sha256-MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI="}]}, {"Route": "uploads/robot leg.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47187"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Apr 2025 01:14:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI="}]}, {"Route": "uploads/sql_1.o2p4fx5oup.txt", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_1.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1657"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 20:49:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o2p4fx5oup"}, {"Name": "label", "Value": "uploads/sql_1.txt"}, {"Name": "integrity", "Value": "sha256-8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4="}]}, {"Route": "uploads/sql_1.txt", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_1.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1657"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 20:49:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4="}]}, {"Route": "uploads/sql_2.o2p4fx5oup.txt", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_2.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1657"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 20:51:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o2p4fx5oup"}, {"Name": "label", "Value": "uploads/sql_2.txt"}, {"Name": "integrity", "Value": "sha256-8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4="}]}, {"Route": "uploads/sql_2.txt", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql_2.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1657"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 20:51:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4="}]}, {"Route": "uploads/sql.o2p4fx5oup.txt", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1657"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 20:45:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o2p4fx5oup"}, {"Name": "label", "Value": "uploads/sql.txt"}, {"Name": "integrity", "Value": "sha256-8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4="}]}, {"Route": "uploads/sql.txt", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\sql.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1657"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 20:45:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8Dp2zBWIlRwq0z+N5o0qrpmhUeYeeieIIjL09rJXn+4="}]}, {"Route": "uploads/test.54emubzsk1.txt", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB+8=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 13:03:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "54emubzsk1"}, {"Name": "label", "Value": "uploads/test.txt"}, {"Name": "integrity", "Value": "sha256-C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB+8="}]}, {"Route": "uploads/test.docx", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6507"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 13:01:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho="}]}, {"Route": "uploads/test.lacrko49p0.docx", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6507"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 13:01:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lacrko49p0"}, {"Name": "label", "Value": "uploads/test.docx"}, {"Name": "integrity", "Value": "sha256-/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho="}]}, {"Route": "uploads/test.txt", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB+8=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 13:03:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB+8="}]}, {"Route": "uploads/text.4rhg4rlloj.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\text.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109894"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 13:28:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4rhg4rlloj"}, {"Name": "label", "Value": "uploads/text.jpg"}, {"Name": "integrity", "Value": "sha256-IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8="}]}, {"Route": "uploads/text.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\text.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109894"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 Jun 2025 13:28:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8="}]}, {"Route": "uploads/vibe_1.4oku5zbii3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:44:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4oku5zbii3"}, {"Name": "label", "Value": "uploads/vibe_1.jpg"}, {"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe_1.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:44:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe_2.4oku5zbii3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:46:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4oku5zbii3"}, {"Name": "label", "Value": "uploads/vibe_2.jpg"}, {"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe_2.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:46:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe.4oku5zbii3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:30:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4oku5zbii3"}, {"Name": "label", "Value": "uploads/vibe.jpg"}, {"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:30:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "workflow-demo-enhanced.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow-demo-enhanced.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27351"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7ARxMipZYjf/8x4gRKc+2lGjbPHka8mSXbQF9+li3pE=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 08:23:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7ARxMipZYjf/8x4gRKc+2lGjbPHka8mSXbQF9+li3pE="}]}, {"Route": "workflow-demo-enhanced.smapltjoiu.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow-demo-enhanced.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27351"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7ARxMipZYjf/8x4gRKc+2lGjbPHka8mSXbQF9+li3pE=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 08:23:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "workflow-demo-enhanced.html"}, {"Name": "integrity", "Value": "sha256-7ARxMipZYjf/8x4gRKc+2lGjbPHka8mSXbQF9+li3pE="}]}, {"Route": "workflow.5ipweew5fc.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Jun 2025 13:30:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "workflow.html"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "workflow.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\workflow.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Jun 2025 13:30:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}]}