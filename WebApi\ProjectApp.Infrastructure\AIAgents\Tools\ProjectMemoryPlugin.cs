using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Threading.Tasks;
using Microsoft.SemanticKernel;
using ProjectApp.Core.Repositories;
using ProjectApp.Core.Dtos;
using Dapper;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class ProjectMemoryPlugin
    {
        private readonly IProjectMemoryRepository _projectMemoryRepository;
        private readonly IDbConnection _dbConnection;

        public ProjectMemoryPlugin(IProjectMemoryRepository projectMemoryRepository, IDbConnection dbConnection)
        {
            _projectMemoryRepository = projectMemoryRepository;
            _dbConnection = dbConnection;
        }

        [KernelFunction("create_project_memory")]
        [Description("Creates a new project memory entry with workspace, description, and category information")]
        public async Task<string> CreateProjectMemory(
            [Description("The workspace name for the project memory")] string workspace,
            [Description("Detailed description of the project")] string projectDescription,
            [Description("The category of the project (e.g., Development, Research, Documentation)")] string projectCategory)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(workspace))
                    return "Error: Workspace name is required.";

                if (string.IsNullOrWhiteSpace(projectDescription))
                    return "Error: Project description is required.";

                if (string.IsNullOrWhiteSpace(projectCategory))
                    return "Error: Project category is required.";

                var projectMemory = new ProjectMemory
                {
                    Workspace = workspace,
                    ProjectDescription = projectDescription,
                    ProjectCategory = projectCategory,
                    Status = "Created"
                };

                var result = await _projectMemoryRepository.SaveProjectMemoryAsync(projectMemory);
                
                return $"Successfully created project memory with ID: {result.Id} for workspace '{workspace}'";
            }
            catch (Exception ex)
            {
                return $"Error creating project memory: {ex.Message}";
            }
        }

        [KernelFunction("update_project_memory")]
        [Description("Updates an existing project memory entry")]
        public async Task<string> UpdateProjectMemory(
            [Description("The ID of the project memory to update")] int projectMemoryId,
            [Description("The workspace name for the project memory")] string workspace,
            [Description("Detailed description of the project")] string projectDescription,
            [Description("The category of the project")] string projectCategory)
        {
            try
            {
                var existingMemory = await _projectMemoryRepository.GetProjectMemoryByIdAsync(projectMemoryId);
                if (existingMemory == null)
                    return $"Error: Project memory with ID {projectMemoryId} not found.";

                existingMemory.Workspace = workspace ?? existingMemory.Workspace;
                existingMemory.ProjectDescription = projectDescription ?? existingMemory.ProjectDescription;
                existingMemory.ProjectCategory = projectCategory ?? existingMemory.ProjectCategory;
                existingMemory.Status = "Updated";

                var result = await _projectMemoryRepository.SaveProjectMemoryAsync(existingMemory);
                
                return $"Successfully updated project memory with ID: {result.Id}";
            }
            catch (Exception ex)
            {
                return $"Error updating project memory: {ex.Message}";
            }
        }

        [KernelFunction("get_project_memories")]
        [Description("Retrieves project memories, optionally filtered by workspace")]
        public async Task<string> GetProjectMemories(
            [Description("Optional workspace name to filter by")] string workspace = null)
        {
            try
            {
                var memories = await _projectMemoryRepository.GetAllProjectMemoriesAsync(workspace);
                
                if (!memories.Any())
                {
                    return workspace != null 
                        ? $"No project memories found for workspace '{workspace}'"
                        : "No project memories found";
                }

                var result = "Project Memories:\n";
                foreach (var memory in memories)
                {
                    result += $"ID: {memory.Id}, Workspace: {memory.Workspace}, Category: {memory.ProjectCategory}, Status: {memory.Status}\n";
                    result += $"Description: {memory.ProjectDescription}\n\n";
                }

                return result;
            }
            catch (Exception ex)
            {
                return $"Error retrieving project memories: {ex.Message}";
            }
        }

        [KernelFunction("get_project_memory_by_id")]
        [Description("Retrieves a specific project memory by its ID")]
        public async Task<string> GetProjectMemoryById(
            [Description("The ID of the project memory to retrieve")] int projectMemoryId)
        {
            try
            {
                var memory = await _projectMemoryRepository.GetProjectMemoryByIdAsync(projectMemoryId);
                
                if (memory == null)
                    return $"Project memory with ID {projectMemoryId} not found.";

                return $"Project Memory ID: {memory.Id}\n" +
                       $"Workspace: {memory.Workspace}\n" +
                       $"Category: {memory.ProjectCategory}\n" +
                       $"Status: {memory.Status}\n" +
                       $"Description: {memory.ProjectDescription}";
            }
            catch (Exception ex)
            {
                return $"Error retrieving project memory: {ex.Message}";
            }
        }

        [KernelFunction("delete_project_memory")]
        [Description("Deletes a project memory entry by its ID")]
        public async Task<string> DeleteProjectMemory(
            [Description("The ID of the project memory to delete")] int projectMemoryId)
        {
            try
            {
                var success = await _projectMemoryRepository.DeleteProjectMemoryAsync(projectMemoryId);
                
                return success 
                    ? $"Successfully deleted project memory with ID: {projectMemoryId}"
                    : $"Project memory with ID {projectMemoryId} not found or could not be deleted.";
            }
            catch (Exception ex)
            {
                return $"Error deleting project memory: {ex.Message}";
            }
        }

        [KernelFunction("bulk_create_project_memories")]
        [Description("Creates multiple project memories from a structured text input. Format: workspace|description|category on each line")]
        public async Task<string> BulkCreateProjectMemories(
            [Description("Multi-line text with format: workspace|description|category per line")] string bulkData)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(bulkData))
                    return "Error: Bulk data is required.";

                var lines = bulkData.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                var results = new List<string>();
                var successCount = 0;
                var errorCount = 0;

                foreach (var line in lines)
                {
                    try
                    {
                        var parts = line.Split('|', StringSplitOptions.TrimEntries);
                        if (parts.Length != 3)
                        {
                            results.Add($"Error on line '{line}': Expected format 'workspace|description|category'");
                            errorCount++;
                            continue;
                        }

                        var result = await CreateProjectMemory(parts[0], parts[1], parts[2]);
                        if (result.StartsWith("Successfully"))
                        {
                            successCount++;
                            results.Add($"✓ {result}");
                        }
                        else
                        {
                            errorCount++;
                            results.Add($"✗ {result}");
                        }
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        results.Add($"Error processing line '{line}': {ex.Message}");
                    }
                }

                var summary = $"Bulk operation completed: {successCount} successful, {errorCount} errors\n\n";
                return summary + string.Join("\n", results);
            }
            catch (Exception ex)
            {
                return $"Error in bulk create operation: {ex.Message}";
            }
        }

        [KernelFunction("extract_and_create_project_memory")]
        [Description("Extracts content from uploaded files and creates project memory entries based on the content")]
        public async Task<string> ExtractAndCreateProjectMemory(
            [Description("The name of the uploaded file to extract content from")] string fileName,
            [Description("The workspace name for the project memory")] string workspace,
            [Description("The project category")] string projectCategory)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return "Error: File name is required.";

                if (string.IsNullOrWhiteSpace(workspace))
                    return "Error: Workspace name is required.";

                if (string.IsNullOrWhiteSpace(projectCategory))
                    return "Error: Project category is required.";

                // Get file description from the Files table
                var fileQuery = "SELECT Description FROM Files WHERE FileName = @FileName";
                var fileDescription = await _dbConnection.QueryFirstOrDefaultAsync<string>(fileQuery, new { FileName = fileName });

                if (string.IsNullOrEmpty(fileDescription))
                    return $"Error: File '{fileName}' not found or has no extracted content.";

                // Create project memory with extracted content
                var result = await CreateProjectMemory(workspace, fileDescription, projectCategory);
                
                return $"File '{fileName}' processed. {result}";
            }
            catch (Exception ex)
            {
                return $"Error extracting and creating project memory from file '{fileName}': {ex.Message}";
            }
        }
    }
}
