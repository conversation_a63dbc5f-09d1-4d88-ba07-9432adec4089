﻿using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;

namespace ProjectApp.Infrastructure.Repositories
{
    public class WorkspaceRepository(IDbConnection _dbConnection,
            IAssignWorkspaceRepository _assignWorkspaceRepository, IExtractEmailFromAccessor _extractEmail) : IWorkspaceRepository
    {

        public async Task<List<WorkspaceDto>> GetAll()
        {
            var workspaces = await _dbConnection.QueryAsync<Workspace>("SELECT * FROM Workspaces");
            var workspaceDtos = new List<WorkspaceDto>();

            foreach (var workspace in workspaces)
            {
                var users = await _assignWorkspaceRepository.GetUsersByWorkspaceId(workspace.Id);

                workspaceDtos.Add(new WorkspaceDto
                {
                    Id = workspace.Id,
                    Title = workspace.Title,
                    Description = workspace.Description,
                    IsDefault = workspace.IsDefault,
                    Members = users,
                    SystemInformation = workspace.SystemInformation,
                    ModelName = workspace.ModelName,
                    IsProjectManagement = workspace.IsProjectManagement,
                    IsFavorite = workspace.IsFavorite
                });
            }

            return workspaceDtos;
        }

        public async Task<WorkspaceDto> GetByTitle(string title)
        {
            var workspace = await _dbConnection.QueryFirstOrDefaultAsync<Workspace>("SELECT * FROM Workspaces WHERE Title = @Title Order By Id DESC", new { Title = title });

            if (workspace == null)
            {
                throw new Exception("Could not find Workspace");
            }

            var users = await _assignWorkspaceRepository.GetUsersByWorkspaceId(workspace.Id);

            return new WorkspaceDto
            {
                Id = workspace.Id,
                Title = workspace.Title,
                Description = workspace.Description,
                IsDefault = workspace.IsDefault,
                Members = users,
                SystemInformation = workspace.SystemInformation,
                ModelName = workspace.ModelName,
                IsProjectManagement = workspace.IsProjectManagement,
                IsFavorite = workspace.IsFavorite
            };
        }

        public async Task<WorkspaceDto> GetById(int id)
        {
            var workspace = await _dbConnection.QueryFirstOrDefaultAsync<Workspace>("SELECT * FROM Workspaces WHERE Id = @Id Order By Id DESC", new { Id = id });

            if (workspace == null)
            {
                throw new Exception("Could not find Workspace");
            }

            var users = await _assignWorkspaceRepository.GetUsersByWorkspaceId(workspace.Id);

            return new WorkspaceDto
            {
                Id = workspace.Id,
                Title = workspace.Title,
                Description = workspace.Description,
                IsDefault = workspace.IsDefault,
                Members = users,
                SystemInformation = workspace.SystemInformation,
                ModelName = workspace.ModelName,
                IsProjectManagement = workspace.IsProjectManagement,
                IsFavorite = workspace.IsFavorite
            };
        }

        public async Task<Workspace> CreateOrUpdate(Workspace workspace)
        {
            var existingWorkspace = await _dbConnection.QueryFirstOrDefaultAsync<Workspace>("SELECT * FROM Workspaces WHERE Title = @Title AND Id != @Id", new { Title = workspace.Title, Id = workspace.Id });

            if (existingWorkspace != null)
            {
                throw new Exception("Workspace title already exists");
            }

            if (workspace.Id == 0)
            {
                var insertQuery = "INSERT INTO Workspaces (Title, Description, IsDefault, SystemInformation, ModelName, IsProjectManagement, IsFavorite) OUTPUT INSERTED.Id VALUES (@Title, @Description, @IsDefault, @SystemInformation, @ModelName, @IsProjectManagement, @IsFavorite)";
                workspace.Id = await _dbConnection.ExecuteScalarAsync<int>(insertQuery, workspace);
            }
            else
            {
                var updateQuery = "UPDATE Workspaces SET Title = @Title, Description = @Description, IsDefault = @IsDefault, SystemInformation = @SystemInformation, ModelName = @ModelName, IsProjectManagement = @IsProjectManagement, IsFavorite = @IsFavorite WHERE Id = @Id";
                await _dbConnection.ExecuteAsync(updateQuery, workspace);
            }

            return workspace;
        }

        public async Task<Workspace> Delete(int id)
        {
            var workspace = await _dbConnection.QueryFirstOrDefaultAsync<Workspace>("SELECT * FROM Workspaces WHERE Id = @Id", new { Id = id });

            if (workspace == null)
            {
                throw new Exception("Could not find Workspace");
            }

            var deleteQuery = "DELETE FROM Workspaces WHERE Id = @Id";
            await _dbConnection.ExecuteAsync(deleteQuery, new { Id = id });
            return workspace;
        }

        public async Task<List<WorkspaceDto>> GetWorkspacesForUser()
        {
            var email = _extractEmail.GetEmail();
            // Get the first user role for this email (roles are comma-separated)
            var roles = await _dbConnection.QueryFirstOrDefaultAsync<string>(
            "SELECT TOP 1 Role FROM UserAccounts WHERE Email = @Email", new { Email = email });

            var firstRole = roles?.Split(',').Select(r => r.Trim()).FirstOrDefault();

            if (firstRole != null && firstRole.Equals("admin", StringComparison.OrdinalIgnoreCase))
            {
            // If admin, return all workspaces
            return await GetAll();
            }
            else
            {
            // Otherwise, return only assigned workspaces
            var sql = @"
                SELECT w.*
                FROM AssignWorkspaces aw
                INNER JOIN Workspaces w ON aw.WorkspaceId = w.Id
                WHERE aw.Email = @Email Order By w.Id DESC";

            var workspaces = await _dbConnection.QueryAsync<Workspace>(sql, new { Email = email });
            var workspaceDtos = new List<WorkspaceDto>();

            foreach (var workspace in workspaces)
            {
                var users = await _assignWorkspaceRepository.GetUsersByWorkspaceId(workspace.Id);

                workspaceDtos.Add(new WorkspaceDto
                {
                Id = workspace.Id,
                Title = workspace.Title,
                Description = workspace.Description,
                IsDefault = workspace.IsDefault,
                SystemInformation = workspace.SystemInformation,
                ModelName = workspace.ModelName,
                IsProjectManagement = workspace.IsProjectManagement,
                IsFavorite = workspace.IsFavorite,
                Members = users
                });
            }

            return workspaceDtos;
            }
        }
        public async Task<Workspace> GetDefaultWorkspace()
        {
            var workspace = await _dbConnection.QueryFirstOrDefaultAsync<Workspace>("SELECT * FROM Workspaces WHERE IsDefault = 1");

            if (workspace == null)
            {
                return null;
            }
            return workspace;
        }
        public async Task<DateTime> GetLastCompletionDate(string assignedEmail)
        {
            var lastCompletionDate = await _dbConnection.QueryFirstOrDefaultAsync<DateTime?>(
                @"SELECT TOP 1 CompletionDate FROM Projects
                      WHERE AssignedEmail = @AssignedEmail
                      ORDER BY CompletionDate DESC",
                new { AssignedEmail = assignedEmail });

            return lastCompletionDate ?? DateTime.Now;
        }

        public async Task<bool> ToggleFavorite(int workspaceId)
        {
            var workspace = await _dbConnection.QueryFirstOrDefaultAsync<Workspace>("SELECT * FROM Workspaces WHERE Id = @Id", new { Id = workspaceId });

            if (workspace == null)
            {
                throw new Exception("Could not find Workspace");
            }

            var newFavoriteStatus = !workspace.IsFavorite;
            var updateQuery = "UPDATE Workspaces SET IsFavorite = @IsFavorite WHERE Id = @Id";
            await _dbConnection.ExecuteAsync(updateQuery, new { IsFavorite = newFavoriteStatus, Id = workspaceId });

            return newFavoriteStatus;
        }

        public async Task<List<WorkspaceDto>> GetFavorites()
        {
            var workspaces = await _dbConnection.QueryAsync<Workspace>("SELECT * FROM Workspaces WHERE IsFavorite = 1 ORDER BY Title");
            var workspaceDtos = new List<WorkspaceDto>();

            foreach (var workspace in workspaces)
            {
                var users = await _assignWorkspaceRepository.GetUsersByWorkspaceId(workspace.Id);

                workspaceDtos.Add(new WorkspaceDto
                {
                    Id = workspace.Id,
                    Title = workspace.Title,
                    Description = workspace.Description,
                    IsDefault = workspace.IsDefault,
                    Members = users,
                    SystemInformation = workspace.SystemInformation,
                    ModelName = workspace.ModelName,
                    IsProjectManagement = workspace.IsProjectManagement,
                    IsFavorite = workspace.IsFavorite
                });
            }

            return workspaceDtos;
        }
    }
}
