﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure;

namespace ProjectApp.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class WorkspaceController : ControllerBase
    {
        private readonly IWorkspaceRepository _workspaceRepo;
        private readonly AIService _aiService;

        public WorkspaceController(IWorkspaceRepository workspaceRepo, AIService aiService)
        {
            _workspaceRepo = workspaceRepo;
            _aiService = aiService;
        }

        [HttpGet("GetAll")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<WorkspaceDto>>> GetAll()
        {
            try
            {
                var res = await _workspaceRepo.GetAll();
                return Ok(res);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetById")]
        [AllowAnonymous]
        public async Task<ActionResult<WorkspaceDto>> GetById(int id)
        {
            try
            {
                var res = await _workspaceRepo.GetById(id);
                return Ok(res);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetByTitle")]
        [AllowAnonymous]
        public async Task<ActionResult<WorkspaceDto>> GetByTitle(string title)
        {
            try
            {
                var res = await _workspaceRepo.GetByTitle(title);
                return Ok(res);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("CreateOrUpdate")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<Workspace>> CreateOrUpdate(Workspace request)
        {
            try
            {
                var res = await _workspaceRepo.CreateOrUpdate(request);

                // Automatically add navigation entry for the new/updated workspace
                try
                {
                    var navigationEntry = new NavigationEntryDto
                    {
                        Id = "Navigation-Chat" + res.Id,
                        Title = res.Title,
                        Description = res.Description ?? $"Chat with {res.Title} workspace",
                        NavigationType = "Workspace",
                        Route = $"/chat/workspace/{res.Title}",
                        Icon = "📁"
                    };

                    await _aiService.AddNavigationEntry(navigationEntry);
                }
                catch (Exception ex)
                {
                    // Log but don't fail the workspace creation if navigation entry fails
                    Console.WriteLine($"Warning: Failed to add navigation entry for workspace {res.Title}: {ex.Message}");
                }

                return Ok(res);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<Workspace>> Delete(int id)
        {
            try
            {
                // Get workspace details before deletion for navigation cleanup
                string workspaceTitle = "";
                try
                {
                    var workspace = await _workspaceRepo.GetById(id);
                    workspaceTitle = workspace?.Title ?? "";
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Warning: Could not retrieve workspace title for navigation cleanup: {ex.Message}");
                }

                var res = await _workspaceRepo.Delete(id);

                // Try to remove navigation entry for deleted workspace
                if (!string.IsNullOrEmpty(workspaceTitle))
                {
                    try
                    {
                        await _aiService.DeleteSingleMemory("Navigation-Chat" + id, "Navigation");
                        Console.WriteLine($"Workspace {workspaceTitle} deleted - navigation entry should be removed");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: Failed to remove navigation entry for deleted workspace {workspaceTitle}: {ex.Message}");
                    }
                }

                return Ok(res);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetWorkspacesByUserEmail")]
        [AllowAnonymous]
        public async Task<ActionResult<List<WorkspaceDto>>> GetWorkspacesForUser()
        {
            try
            {
                var res = await _workspaceRepo.GetWorkspacesForUser();
                return Ok(res);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("ToggleFavorite")]
        public async Task<ActionResult<bool>> ToggleFavorite(int workspaceId)
        {
            try
            {
                var newFavoriteStatus = await _workspaceRepo.ToggleFavorite(workspaceId);
                return Ok(newFavoriteStatus);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetFavorites")]
        public async Task<ActionResult<List<WorkspaceDto>>> GetFavorites()
        {
            try
            {
                var res = await _workspaceRepo.GetFavorites();
                return Ok(res);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
